<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="AI ContentCraft - 强大的内容创作工具，具备故事生成、文本转语音和图像生成功能">
    <meta name="keywords" content="AI, 内容创作, 故事生成, 文本转语音, TTS, 播客, 图像生成">
    <meta name="author" content="AI ContentCraft">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎨</text></svg>">

    <title>AI ContentCraft - 智能内容创作平台</title>

    <!-- External Stylesheets -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="modern-styles.css">

    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only">Skip to main content</a>

    <div class="container">
        <!-- Header with navigation -->
        <header role="banner">
            <nav class="nav-bar" role="navigation" aria-label="主导航">
                <button class="nav-button active" onclick="switchPage('story')" aria-pressed="true" aria-describedby="story-desc">
                    <span>📖</span> 故事生成器
                </button>
                <button class="nav-button" onclick="switchPage('simple')" aria-pressed="false" aria-describedby="simple-desc">
                    <span>🗣️</span> 简单语音合成
                </button>
                <button class="nav-button" onclick="switchPage('multi')" aria-pressed="false" aria-describedby="multi-desc">
                    <span>🎭</span> 多声音语音合成
                </button>
                <button class="nav-button" onclick="switchPage('podcast')" aria-pressed="false" aria-describedby="podcast-desc">
                    <span>🎙️</span> 播客生成器
                </button>
            </nav>

            <!-- Hidden descriptions for screen readers -->
            <div class="sr-only">
                <div id="story-desc">生成故事并转换为带图像的脚本</div>
                <div id="simple-desc">使用单一声音将文本转换为语音</div>
                <div id="multi-desc">使用不同说话者创建多声音音频</div>
                <div id="podcast-desc">生成多主持人播客内容</div>
            </div>
        </header>

        <!-- Main content area -->
        <main id="main-content" role="main">

            <!-- Story Generator Page -->
            <section id="storyPage" class="page active" aria-labelledby="story-title">
                <div class="story-page-layout">
                    <!-- Left Side: Instructions Panel -->
                    <div class="instructions-panel">
                        <div class="instructions-header">
                            <h2>📋 操作指南</h2>
                            <p class="instructions-subtitle">按照以下步骤创建您的故事</p>
                        </div>

                        <div class="instruction-steps">
                            <div class="instruction-step">
                                <div class="step-number">1</div>
                                <div class="step-content">
                                    <h3>输入故事主题</h3>
                                    <p>在右侧输入框中描述您想要的故事主题，例如"太空冒险"、"魔法森林"等</p>
                                </div>
                                <div class="step-arrow">→</div>
                            </div>

                            <div class="instruction-step">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                    <h3>选择语言</h3>
                                    <p>选择您希望生成故事的语言：中文或英文</p>
                                </div>
                                <div class="step-arrow">→</div>
                            </div>

                            <div class="instruction-step">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                    <h3>生成故事</h3>
                                    <p>点击"生成故事"按钮，AI将为您创作一个精彩的故事</p>
                                </div>
                                <div class="step-arrow">→</div>
                            </div>

                            <div class="instruction-step">
                                <div class="step-number">4</div>
                                <div class="step-content">
                                    <h3>转换为脚本</h3>
                                    <p>故事生成后，可以将其转换为包含对话和旁白的脚本格式</p>
                                </div>
                                <div class="step-arrow">→</div>
                            </div>

                            <div class="instruction-step">
                                <div class="step-number">5</div>
                                <div class="step-content">
                                    <h3>生成音频和图片</h3>
                                    <p>为每个脚本段落生成语音和配图，创建完整的多媒体故事</p>
                                </div>
                            </div>
                        </div>

                        <div class="tips-section">
                            <h3>💡 小贴士</h3>
                            <ul class="tips-list">
                                <li>主题描述越详细，生成的故事越精彩</li>
                                <li>可以指定故事类型，如"科幻"、"童话"、"悬疑"等</li>
                                <li>生成后可以编辑故事内容，让它更符合您的需求</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Right Side: Story Generation Section -->
                    <div class="story-generation-panel">
                        <div class="story-container">
                            <h1 id="story-title">✨ 故事生成</h1>
                            <div class="form-group">
                                <label for="themeInput" class="sr-only">故事主题</label>
                                <input
                                    type="text"
                                    id="themeInput"
                                    class="theme-input"
                                    placeholder="输入您的故事主题（例如：'太空中的神奇冒险'）..."
                                    aria-describedby="theme-help"
                                    autocomplete="off"
                                >
                                <div id="theme-help" class="sr-only">为您的故事输入主题或话题</div>
                            </div>

                            <!-- Language Selection for Story -->
                            <div class="form-group">
                                <label class="language-label">选择故事语言 / Choose Story Language:</label>
                                <div class="language-options">
                                    <label class="radio-option">
                                        <input type="radio" name="storyLanguage" value="chinese" checked>
                                        <span class="radio-text">🇨🇳 中文</span>
                                    </label>
                                    <label class="radio-option">
                                        <input type="radio" name="storyLanguage" value="english">
                                        <span class="radio-text">🇺🇸 English</span>
                                    </label>
                                </div>
                            </div>
                            <div class="button-container">
                                <button onclick="generateStory()" id="generateStoryBtn" class="primary-btn" aria-describedby="generate-help">
                                    <span class="spinner" id="storySpinner"></span>
                                    <span id="generateStoryText">🎯 生成故事</span>
                                </button>
                            </div>
                            <div id="generate-help" class="sr-only">点击根据您的主题生成故事</div>

                            <div class="form-group">
                                <label for="storyText" class="sr-only">生成的故事内容</label>
                                <textarea
                                    id="storyText"
                                    class="text-input"
                                    placeholder="您生成的故事将在这里显示..."
                                    aria-describedby="story-help"
                                    readonly
                                ></textarea>
                                <div id="story-help" class="sr-only">生成的故事内容将在此处显示</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Script Conversion Section -->
                <div class="script-container">
                    <h2 id="script-title">🎬 脚本转换</h2>
                    <div class="script-buttons">
                        <button onclick="generateScript()" id="convertToScript" class="btn primary convert-script" aria-describedby="convert-help">
                            <span class="spinner" id="convertSpinner"></span>
                            <span id="convertText">📝 转换为脚本</span>
                        </button>
                        <div class="button-row">
                            <button onclick="generateStoryAudio()" id="generateStoryAudioBtn" class="btn secondary hidden" aria-describedby="audio-help">
                                <span class="spinner" id="audioSpinner"></span>
                                <span id="storyBtnText">🎵 生成音频</span>
                            </button>
                            <button onclick="downloadAllImages()" id="downloadImages" class="btn secondary hidden" aria-describedby="download-help">
                                <span>📥</span> 下载所有图片
                            </button>
                        </div>
                    </div>

                    <!-- Hidden help text for screen readers -->
                    <div class="sr-only">
                        <div id="convert-help">将您的故事转换为包含对话和旁白的脚本格式</div>
                        <div id="audio-help">从脚本部分生成音频</div>
                        <div id="download-help">将所有生成的图片下载为zip文件</div>
                    </div>

                    <div id="scriptSections" role="region" aria-label="脚本部分">
                        <!-- Script sections will be dynamically generated here -->
                    </div>

                    <div class="progress-container hidden" id="storyProgressContainer" role="status" aria-live="polite">
                        <div class="progress-text">
                            正在生成：<span id="storyCurrentProgress">0</span>/<span id="storyTotalSections">0</span>
                        </div>
                        <div class="progress-bar" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0">
                            <div class="progress" id="storyProgress"></div>
                        </div>
                    </div>

                    <div class="audio-container">
                        <label for="storyAudioPlayer" class="sr-only">生成的故事音频</label>
                        <audio id="storyAudioPlayer" controls preload="none" aria-describedby="audio-player-help">
                            <source src="" type="audio/mp3">
                            您的浏览器不支持音频元素。
                        </audio>
                        <div id="audio-player-help" class="sr-only">生成故事的音频播放器</div>
                    </div>
                </div>
            </section>

            <!-- Simple TTS Page -->
            <section id="simplePage" class="page" aria-labelledby="simple-title">
                <div class="simple-tts-container">
                    <h1 id="simple-title">🗣️ 简单文本转语音</h1>

                    <div class="form-group">
                        <label for="simpleTtsText" class="sr-only">要转换为语音的文本</label>
                        <textarea
                            id="simpleTtsText"
                            class="text-input"
                            placeholder="输入您想要转换为语音的文本..."
                            aria-describedby="simple-text-help"
                            style="min-height: 150px;"
                        ></textarea>
                        <div id="simple-text-help" class="sr-only">输入任何文本以将其转换为语音</div>
                    </div>

                    <div class="voice-select-container">
                        <label for="simpleTtsVoice" class="form-label">🎭 选择声音：</label>
                        <select id="simpleTtsVoice" class="voice-select" aria-describedby="voice-help">
                            <option value="">正在加载声音...</option>
                        </select>
                        <div id="voice-help" class="sr-only">为文本转语音转换选择声音</div>
                    </div>

                    <div class="button-container">
                        <button onclick="generateSimpleTTS()" id="simpleTtsBtn" class="primary-btn" aria-describedby="simple-generate-help">
                            <span class="spinner" id="simpleTtsSpinner"></span>
                            <span id="simpleTtsButtonText">🎵 生成音频</span>
                        </button>
                    </div>
                    <div id="simple-generate-help" class="sr-only">从输入的文本生成音频</div>

                    <div class="audio-container">
                        <label for="simpleTtsPlayer" class="sr-only">生成的音频</label>
                        <audio id="simpleTtsPlayer" controls preload="none" aria-describedby="simple-audio-help">
                            <source src="" type="audio/mp3">
                            您的浏览器不支持音频元素。
                        </audio>
                        <div id="simple-audio-help" class="sr-only">生成语音的音频播放器</div>
                    </div>
                </div>
            </section>

            <!-- Multi-Voice TTS Page -->
            <section id="multiPage" class="page" aria-labelledby="multi-title">
                <div class="multi-tts-container">
                    <h1 id="multi-title">🎭 多声音文本转语音</h1>

                    <div class="tabs-container">
                        <div class="tabs" role="tablist" aria-label="文本部分">
                            <button class="tab-btn active" onclick="switchTab(0)" role="tab" aria-selected="true" aria-controls="panel-0" id="tab-0">
                                📝 部分 1
                            </button>
                            <button class="tab-btn" onclick="switchTab(1)" role="tab" aria-selected="false" aria-controls="panel-1" id="tab-1">
                                📝 部分 2
                            </button>
                            <button class="tab-btn add-tab" onclick="addNewTab()" aria-label="添加新部分">
                                ➕
                            </button>
                        </div>

                        <div class="tab-panels">
                            <div class="tab-panel active" role="tabpanel" aria-labelledby="tab-0" id="panel-0">
                                <div class="form-group">
                                    <label for="text-0" class="sr-only">部分1的文本</label>
                                    <textarea
                                        id="text-0"
                                        class="text-input"
                                        placeholder="输入此部分的文本..."
                                        aria-describedby="text-help-0"
                                    ></textarea>
                                    <div id="text-help-0" class="sr-only">输入要由所选声音朗读的文本</div>
                                </div>
                                <div class="voice-select-container">
                                    <label for="voice-0" class="form-label">🎭 声音：</label>
                                    <select id="voice-0" class="voice-select" aria-describedby="voice-help-0">
                                        <option value="">正在加载声音...</option>
                                    </select>
                                    <div id="voice-help-0" class="sr-only">为此部分选择声音</div>
                                </div>
                            </div>

                            <div class="tab-panel" role="tabpanel" aria-labelledby="tab-1" id="panel-1">
                                <div class="form-group">
                                    <label for="text-1" class="sr-only">部分2的文本</label>
                                    <textarea
                                        id="text-1"
                                        class="text-input"
                                        placeholder="输入此部分的文本..."
                                        aria-describedby="text-help-1"
                                    ></textarea>
                                    <div id="text-help-1" class="sr-only">输入要由所选声音朗读的文本</div>
                                </div>
                                <div class="voice-select-container">
                                    <label for="voice-1" class="form-label">🎭 声音：</label>
                                    <select id="voice-1" class="voice-select" aria-describedby="voice-help-1">
                                        <option value="">正在加载声音...</option>
                                    </select>
                                    <div id="voice-help-1" class="sr-only">为此部分选择声音</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="button-container">
                        <button onclick="generateAllAudio()" id="generateBtn" class="primary-btn" aria-describedby="generate-all-help">
                            <span class="spinner" id="spinner"></span>
                            <span id="btnText">🎵 生成所有音频</span>
                        </button>
                        <button onclick="exportMergedAudio()" id="exportBtn" class="action-btn" aria-describedby="export-help">
                            <span>📥</span> 导出合并音频
                        </button>
                    </div>

                    <!-- Hidden help text -->
                    <div class="sr-only">
                        <div id="generate-all-help">为所有部分生成音频并将它们合并在一起</div>
                        <div id="export-help">下载合并的音频文件</div>
                    </div>

                    <div class="progress-container hidden" id="progressContainer" role="status" aria-live="polite">
                        <div class="progress-text">正在处理部分...</div>
                        <div class="progress-bar" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0">
                            <div class="progress" id="progress"></div>
                        </div>
                    </div>

                    <div class="audio-container">
                        <label for="multiAudioPlayer" class="sr-only">生成的合并音频</label>
                        <audio id="multiAudioPlayer" controls preload="none" aria-describedby="multi-audio-help">
                            <source src="" type="audio/mp3">
                            您的浏览器不支持音频元素。
                        </audio>
                        <div id="multi-audio-help" class="sr-only">合并多声音音频的音频播放器</div>
                    </div>

                    <div class="history-container" id="historyContainer" role="region" aria-label="生成历史">
                        <!-- History items will be dynamically added here -->
                    </div>
                </div>
            </section>

            <!-- Podcast Generator Page -->
            <section id="podcastPage" class="page" aria-labelledby="podcast-title">
                <div class="podcast-page-layout">
                    <!-- Left Side: Instructions Panel -->
                    <div class="instructions-panel">
                        <div class="instructions-header">
                            <h2>📋 操作指南</h2>
                            <p class="instructions-subtitle">按照以下步骤创建您的播客</p>
                        </div>

                        <div class="instruction-steps">
                            <div class="instruction-step">
                                <div class="step-number">1</div>
                                <div class="step-content">
                                    <h3>输入播客主题</h3>
                                    <p>在右侧输入框中描述您的播客主题、大纲或内容，可以尽可能详细</p>
                                </div>
                                <div class="step-arrow">→</div>
                            </div>

                            <div class="instruction-step">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                    <h3>选择语言</h3>
                                    <p>选择您希望生成播客的语言：中文或英文</p>
                                </div>
                                <div class="step-arrow">→</div>
                            </div>

                            <div class="instruction-step">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                    <h3>生成播客内容</h3>
                                    <p>点击"生成播客内容"按钮，AI将为您创作播客对话内容</p>
                                </div>
                                <div class="step-arrow">→</div>
                            </div>

                            <div class="instruction-step">
                                <div class="step-number">4</div>
                                <div class="step-content">
                                    <h3>选择主持人声音</h3>
                                    <p>为主持人A和主持人B选择合适的声音，创造不同的角色特色</p>
                                </div>
                                <div class="step-arrow">→</div>
                            </div>

                            <div class="instruction-step">
                                <div class="step-number">5</div>
                                <div class="step-content">
                                    <h3>转换为脚本</h3>
                                    <p>将播客内容转换为分段脚本，便于编辑和音频生成</p>
                                </div>
                                <div class="step-arrow">→</div>
                            </div>

                            <div class="instruction-step">
                                <div class="step-number">6</div>
                                <div class="step-content">
                                    <h3>生成播客音频</h3>
                                    <p>为每个对话段落生成语音，创建完整的播客音频文件</p>
                                </div>
                            </div>
                        </div>

                        <div class="tips-section">
                            <h3>💡 小贴士</h3>
                            <ul class="tips-list">
                                <li>主题描述越详细，生成的播客内容越丰富</li>
                                <li>可以指定播客类型，如"访谈"、"新闻"、"教育"等</li>
                                <li>生成后可以编辑对话内容，调整主持人的发言</li>
                                <li>选择不同的声音可以创造更好的对话效果</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Right Side: Podcast Generation Panel -->
                    <div class="podcast-generation-panel">
                        <div class="podcast-container">
                            <h1 id="podcast-title">🎙️ 播客生成器</h1>

                            <!-- Topic Input Section -->
                            <div class="topic-section">
                                <div class="form-group">
                                    <label for="podcastTopic" class="form-label">📝 播客主题或内容：</label>
                                    <textarea
                                        id="podcastTopic"
                                        class="topic-input"
                                        placeholder="在此输入您的播客主题、大纲或内容。可以尽可能详细..."
                                        rows="6"
                                        aria-describedby="topic-help"
                                    ></textarea>
                                    <div id="topic-help" class="sr-only">输入您播客的主题或内容</div>
                                </div>

                                <!-- Language Selection for Podcast -->
                                <div class="form-group">
                                    <label class="language-label">选择播客语言 / Choose Podcast Language:</label>
                                    <div class="language-options">
                                        <label class="radio-option">
                                            <input type="radio" name="podcastLanguage" value="chinese" checked>
                                            <span class="radio-text">🇨🇳 中文</span>
                                        </label>
                                        <label class="radio-option">
                                            <input type="radio" name="podcastLanguage" value="english">
                                            <span class="radio-text">🇺🇸 English</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="button-container">
                                    <button onclick="generatePodcastContent()" id="generatePodcastBtn" class="primary-btn" aria-describedby="podcast-generate-help">
                                        <span class="spinner" id="podcastSpinner"></span>
                                        <span id="podcastBtnText">🎯 生成播客内容</span>
                                    </button>
                                </div>
                                <div id="podcast-generate-help" class="sr-only">根据您的主题生成播客内容</div>
                            </div>

                            <!-- Script Editor Section -->
                            <div class="script-editor hidden" id="podcastScriptEditor">
                                <div class="voice-selection">
                                    <h3>🎭 为主持人选择声音</h3>
                                    <div class="character-voices">
                                        <div class="voice-select-group">
                                            <label for="hostAVoice">🎤 主持人 A：</label>
                                            <select id="hostAVoice" class="voice-select" aria-describedby="host-a-help">
                                                <option value="">正在加载声音...</option>
                                            </select>
                                            <div id="host-a-help" class="sr-only">为第一位播客主持人选择声音</div>
                                        </div>
                                        <div class="voice-select-group">
                                            <label for="hostBVoice">🎤 主持人 B：</label>
                                            <select id="hostBVoice" class="voice-select" aria-describedby="host-b-help">
                                                <option value="">正在加载声音...</option>
                                            </select>
                                            <div id="host-b-help" class="sr-only">为第二位播客主持人选择声音</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="script-controls">
                                    <button onclick="generatePodcastScript()" id="generateScriptBtn" class="btn primary convert-script" aria-describedby="script-help">
                                        <span>📝</span> 转换为脚本
                                    </button>
                                    <button onclick="addPodcastDialog('A')" class="action-btn" aria-describedby="add-a-help">
                                        <span>➕</span> 添加主持人 A
                                    </button>
                                    <button onclick="addPodcastDialog('B')" class="action-btn" aria-describedby="add-b-help">
                                        <span>➕</span> 添加主持人 B
                                    </button>
                                    <button onclick="translateAndDownload()" id="translateBtn" class="action-btn" aria-describedby="translate-help">
                                        <span>🌐</span> 翻译并下载
                                    </button>
                                </div>

                                <!-- Hidden help text -->
                                <div class="sr-only">
                                    <div id="script-help">将内容转换为对话脚本格式</div>
                                    <div id="add-a-help">为主持人A添加新的对话部分</div>
                                    <div id="add-b-help">为主持人B添加新的对话部分</div>
                                    <div id="translate-help">翻译内容并下载脚本</div>
                                </div>

                                <div id="podcastSections" class="podcast-sections" role="region" aria-label="播客对话部分">
                                    <!-- Dialogue sections will be dynamically generated here -->
                                </div>

                                <div class="button-container">
                                    <button onclick="generatePodcastAudio()" id="generatePodcastAudioBtn" class="primary-btn" aria-describedby="podcast-audio-help">
                                        <span class="spinner" id="podcastAudioSpinner"></span>
                                        <span id="podcastAudioBtnText">🎵 生成播客音频</span>
                                    </button>
                                </div>
                                <div id="podcast-audio-help" class="sr-only">为整个播客生成音频</div>

                                <div class="progress-container hidden" id="podcastProgressContainer" role="status" aria-live="polite">
                                    <div class="progress-text">
                                        正在生成：<span id="podcastCurrentProgress">0</span>/<span id="podcastTotalSections">0</span>
                                    </div>
                                    <div class="progress-bar" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0">
                                        <div class="progress" id="podcastProgress"></div>
                                    </div>
                                </div>

                                <div class="audio-container">
                                    <label for="podcastAudioPlayer" class="sr-only">生成的播客音频</label>
                                    <audio id="podcastAudioPlayer" controls preload="none" aria-describedby="podcast-player-help">
                                        <source src="" type="audio/mp3">
                                        您的浏览器不支持音频元素。
                                    </audio>
                                    <div id="podcast-player-help" class="sr-only">生成播客的音频播放器</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer role="contentinfo" class="footer">
            <p>&copy; 2025 AI ContentCraft. Built with ❤️ for creators.</p>
        </footer>
    </div>

    <script>
        let voices = [];

        // ===== NOTIFICATION SYSTEM =====
        function showNotification(message, type = 'info', duration = 5000) {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(n => n.remove());

            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.setAttribute('role', 'alert');
            notification.setAttribute('aria-live', 'polite');

            const icon = {
                'success': '✅',
                'error': '❌',
                'warning': '⚠️',
                'info': 'ℹ️'
            }[type] || 'ℹ️';

            notification.innerHTML = `
                <div class="notification-content">
                    <span class="notification-icon">${icon}</span>
                    <span class="notification-message">${message}</span>
                    <button class="notification-close" onclick="this.parentElement.parentElement.remove()" aria-label="Close notification">×</button>
                </div>
            `;

            document.body.appendChild(notification);

            // Auto-remove after duration
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.opacity = '0';
                    notification.style.transform = 'translateY(-100%)';
                    setTimeout(() => notification.remove(), 300);
                }
            }, duration);
        }

        // ===== LOADING STATE MANAGEMENT =====
        function setLoadingState(element, isLoading, loadingText = 'Loading...', originalText = '') {
            if (isLoading) {
                element.disabled = true;
                element.classList.add('loading');
                const spinner = element.querySelector('.spinner');
                if (spinner) spinner.style.display = 'inline-block';

                const textElement = element.querySelector('span:not(.spinner)');
                if (textElement) {
                    textElement.dataset.originalText = textElement.textContent;
                    textElement.textContent = loadingText;
                }
            } else {
                element.disabled = false;
                element.classList.remove('loading');
                const spinner = element.querySelector('.spinner');
                if (spinner) spinner.style.display = 'none';

                const textElement = element.querySelector('span:not(.spinner)');
                if (textElement) {
                    textElement.textContent = textElement.dataset.originalText || originalText;
                }
            }
        }

        // ===== ACCESSIBILITY HELPERS =====
        function announceToScreenReader(message) {
            const announcement = document.createElement('div');
            announcement.setAttribute('aria-live', 'polite');
            announcement.setAttribute('aria-atomic', 'true');
            announcement.className = 'sr-only';
            announcement.textContent = message;

            document.body.appendChild(announcement);
            setTimeout(() => announcement.remove(), 1000);
        }

        // ===== FORM VALIDATION =====
        function validateForm(formElement) {
            const requiredFields = formElement.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('error');
                    field.setAttribute('aria-invalid', 'true');
                    isValid = false;
                } else {
                    field.classList.remove('error');
                    field.setAttribute('aria-invalid', 'false');
                }
            });

            return isValid;
        }

        // ===== ELEMENT VISIBILITY HELPERS =====
        function showElement(element) {
            if (typeof element === 'string') {
                element = document.getElementById(element);
            }
            if (element) {
                element.classList.remove('hidden');
                element.style.display = '';
            }
        }

        function hideElement(element) {
            if (typeof element === 'string') {
                element = document.getElementById(element);
            }
            if (element) {
                element.classList.add('hidden');
            }
        }

        function toggleElement(element, show) {
            if (show) {
                showElement(element);
            } else {
                hideElement(element);
            }
        }

        async function loadVoices() {
            try {
                const response = await fetch('/voices');
                voices = await response.json();
                
                // 更新所有语音选择下拉框
                updateAllVoiceSelects();
                
                // 更新简单TTS的语音选择
                const simpleTtsVoice = document.getElementById('simpleTtsVoice');
                if (simpleTtsVoice) {
                    const voiceOptionsHtml = voices.map(voice => 
                        `<option value="${voice.id}">${voice.name} (${voice.language}, ${voice.gender})</option>`
                    ).join('');
                    simpleTtsVoice.innerHTML = voiceOptionsHtml;
                    simpleTtsVoice.value = 'Wise_Woman';
                }
            } catch (error) {
                console.error('加载声音失败:', error);
                voices = [
                    { id: "Wise_Woman", name: "Wise Woman (Default)", language: "en-us", gender: "Female" }
                ];
                updateAllVoiceSelects();
            }
        }

        function updateAllVoiceSelects() {
            const voiceSelects = document.querySelectorAll('.voice-select');
            const voiceOptionsHtml = voices.map(voice =>
                `<option value="${voice.id}">${voice.name} (${voice.language}, ${voice.gender})</option>`
            ).join('');

            voiceSelects.forEach(select => {
                select.innerHTML = voiceOptionsHtml;

                // 为播客主持人设置默认语音
                if (select.id === 'hostAVoice' || select.id === 'hostBVoice') {
                    const podcastLanguage = document.getElementById('podcastTopic')?.getAttribute('data-language') || 'english';
                    const gender = select.id === 'hostAVoice' ? 'Female' : 'Male';
                    select.value = getDefaultVoiceForLanguage(podcastLanguage, gender);
                } else {
                    select.value = 'Wise_Woman';
                }
            });
        }

        // 根据语言选择合适的默认语音
        function getDefaultVoiceForLanguage(language, gender = 'Female') {
            // 所有语音都支持多语言，根据性别选择合适的默认语音
            const suitableVoices = voices.filter(voice => voice.gender === gender);

            if (suitableVoices.length > 0) {
                // 根据语言和性别选择推荐的语音
                if (language === 'chinese') {
                    if (gender === 'Female') {
                        return 'Wise_Woman'; // 智慧女声
                    } else if (gender === 'Male') {
                        return 'Deep_Voice_Man'; // 深沉男声
                    }
                } else {
                    if (gender === 'Female') {
                        return 'Calm_Woman'; // 平静女声
                    } else if (gender === 'Male') {
                        return 'Elegant_Man'; // 优雅男声
                    }
                }
                return suitableVoices[0].id;
            }

            // 最后的备选方案
            return 'Wise_Woman';
        }

        // 简单的语言检测函数
        function detectLanguage(text) {
            // 检测中文字符
            const chineseRegex = /[\u4e00-\u9fff]/;
            if (chineseRegex.test(text)) {
                return 'chinese';
            }
            return 'english';
        }

        // 页面加载时获取声音列表
        loadVoices();

        // 添加播客语言选择变化的事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            const podcastLanguageRadios = document.querySelectorAll('input[name="podcastLanguage"]');
            podcastLanguageRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    updatePodcastVoiceDefaults(this.value);
                });
            });
        });

        // 更新播客主持人的默认语音
        function updatePodcastVoiceDefaults(language) {
            const hostAVoice = document.getElementById('hostAVoice');
            const hostBVoice = document.getElementById('hostBVoice');

            if (hostAVoice && hostBVoice) {
                hostAVoice.value = getDefaultVoiceForLanguage(language, 'Female');
                hostBVoice.value = getDefaultVoiceForLanguage(language, 'Male');
            }
        }

        function startProgressSimulation() {
            progress.style.width = '0%';
            let width = 0;
            const interval = setInterval(() => {
                if (width >= 90) {
                    clearInterval(interval);
                    return;
                }
                width += Math.random() * 10;
                progress.style.width = Math.min(width, 90) + '%';
            }, 200);
        }

        function addToHistory(filename, text, voice) {
            const item = document.createElement('div');
            item.className = 'history-item';
            const timestamp = new Date().toLocaleTimeString();
            item.innerHTML = `
                <span>${timestamp}</span>
                <audio controls src="${filename}"></audio>
                <div class="history-text">
                    <div>${text.substring(0, 30)}${text.length > 30 ? '...' : ''}</div>
                    <div class="voice-name">Voice: ${voice}</div>
                </div>
            `;
            historyContainer.insertBefore(item, historyContainer.firstChild);
        }

        let currentTab = 0;
        let audioBuffers = [];

        function switchTab(index) {
            const tabs = document.querySelectorAll('.tab-btn:not(.add-tab)');
            const panels = document.querySelectorAll('.tab-panel');
            
            tabs.forEach(tab => tab.classList.remove('active'));
            panels.forEach(panel => panel.classList.remove('active'));
            
            tabs[index].classList.add('active');
            panels[index].classList.add('active');
            currentTab = index;
        }

        function addNewTab() {
            const tabs = document.querySelector('.tabs');
            const panels = document.querySelector('.tab-panels');
            const newIndex = document.querySelectorAll('.tab-btn:not(.add-tab)').length;
            
            // 添加新标签按钮
            const tabBtn = document.createElement('button');
            tabBtn.className = 'tab-btn';
            tabBtn.textContent = `部分 ${newIndex + 1}`;
            tabBtn.onclick = () => switchTab(newIndex);
            tabs.insertBefore(tabBtn, tabs.lastElementChild);
            
            // 添加新面板
            const panel = document.createElement('div');
            panel.className = 'tab-panel';
            panel.innerHTML = `
                <textarea class="text-input" placeholder="输入要转换为语音的文本..."></textarea>
                <div class="voice-select-container">
                    <select class="voice-select">
                        ${document.querySelector('.voice-select').innerHTML}
                    </select>
                </div>
            `;
            panels.appendChild(panel);
            
            // 设置默认语音为 Wise Woman
            const voiceSelect = panel.querySelector('.voice-select');
            voiceSelect.value = 'Wise_Woman';
            
            // 切换到新标签
            switchTab(newIndex);
        }

        async function generateAllAudio() {
            const panels = document.querySelectorAll('.tab-panel');
            const generateBtn = document.getElementById('generateBtn');
            const spinner = document.getElementById('spinner');
            const btnText = document.getElementById('btnText');
            const progressContainer = document.getElementById('storyProgressContainer');
            const progress = document.getElementById('storyProgress');

            const sectionsData = Array.from(panels).map(panel => ({
                text: panel.querySelector('.text-input').value.trim(),
                voice: panel.querySelector('.voice-select').value
            })).filter(s => s.text && s.voice);

            if (sectionsData.length === 0) {
                showNotification('请输入要转换的文本', 'warning');
                return;
            }

            // 设置加载状态
            generateBtn.disabled = true;
            spinner.style.display = 'inline-block';
            btnText.textContent = '🎵 正在生成音频...';
            progressContainer.style.display = 'block';
            progress.style.width = '0%';

            // 显示开始生成的通知
            showNotification('开始生成音频，请稍候...', 'info');

            try {
                // 获取故事的语言信息
                const storyLanguage = document.getElementById('storyText').getAttribute('data-language') || 'english';

                const response = await fetch('/generate-and-merge', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ sections: sectionsData, language: storyLanguage })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let audioGenerated = false;

                while (true) {
                    const { value, done } = await reader.read();
                    if (done) break;

                    const lines = decoder.decode(value).split('\n');
                    for (const line of lines) {
                        if (!line.trim()) continue;

                        try {
                            const data = JSON.parse(line);
                            if (data.type === 'progress') {
                                progress.style.width = `${(data.current / data.total) * 100}%`;
                            } else if (data.type === 'complete' && data.success) {
                                const audioPlayer = document.getElementById('multiAudioPlayer');
                                audioPlayer.src = data.audioUrl || data.filename;
                                audioPlayer.load();

                                // 启用导出按钮
                                document.getElementById('exportBtn').disabled = false;

                                // 添加到历史记录
                                addToHistory(data.filename, `合并音频 (${sectionsData.length} 个部分)`, '多个');

                                // 标记音频已生成
                                audioGenerated = true;

                                // 显示成功通知
                                showNotification('🎉 音频生成成功！', 'success');

                                // 滚动到音频播放器
                                audioPlayer.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            } else if (data.type === 'error') {
                                throw new Error(data.message || '生成过程中发生错误');
                            }
                        } catch (e) {
                            console.warn('Failed to parse line:', line, e);
                        }
                    }
                }

                // 如果没有收到成功消息，显示警告
                if (!audioGenerated) {
                    showNotification('音频生成可能未完成，请检查结果', 'warning');
                }

            } catch (error) {
                console.error('Audio generation error:', error);
                showNotification('生成音频失败：' + error.message, 'error');
            } finally {
                // 恢复按钮状态
                generateBtn.disabled = false;
                spinner.style.display = 'none';
                btnText.textContent = '🎵 生成所有音频';

                // 延迟隐藏进度条
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                    progress.style.width = '0%';
                }, 2000);
            }
        }

        async function exportMergedAudio() {
            const audioPlayer = document.getElementById('multiAudioPlayer');
            if (!audioPlayer.src) {
                alert('请先生成音频');
                return;
            }

            try {
                // 创建下载链接
                const link = document.createElement('a');
                link.href = audioPlayer.src;
                link.download = `merged-${new Date().toISOString().replace(/[:.]/g, '-')}.wav`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } catch (error) {
                alert('导出失败：' + error.message);
            }
        }

        async function generateStory() {
            const theme = document.getElementById('themeInput').value.trim();
            if (!theme) {
                showNotification('请输入故事主题', 'warning');
                document.getElementById('themeInput').focus();
                return;
            }

            // Get selected language
            const selectedLanguage = document.querySelector('input[name="storyLanguage"]:checked').value;

            const generateStoryBtn = document.getElementById('generateStoryBtn');
            const storyText = document.getElementById('storyText');
            const spinner = document.getElementById('storySpinner');
            const btnText = document.getElementById('generateStoryText');

            // Update UI state
            generateStoryBtn.disabled = true;
            spinner.style.display = 'inline-block';
            btnText.textContent = '正在生成故事...';
            storyText.value = '';

            try {
                const response = await fetch('/generate-story', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ theme, language: selectedLanguage })
                });

                const data = await response.json();
                if (data.success) {
                    storyText.value = data.story;
                    storyText.removeAttribute('readonly');

                    // 保存故事语言信息到数据属性
                    storyText.setAttribute('data-language', data.language || selectedLanguage);

                    showNotification('故事生成成功！', 'success');

                    // Scroll to story text for better UX
                    storyText.scrollIntoView({ behavior: 'smooth', block: 'center' });
                } else {
                    throw new Error(data.error || '生成故事失败');
                }
            } catch (error) {
                console.error('Story generation error:', error);
                showNotification('生成故事失败：' + error.message, 'error');
            } finally {
                generateStoryBtn.disabled = false;
                spinner.style.display = 'none';
                btnText.textContent = '🎯 生成故事';
            }
        }

        async function generateScript() {
            const storyText = document.getElementById('storyText').value;
            if (!storyText) {
                alert('请先生成一个故事');
                return;
            }

            const convertToScript = document.getElementById('convertToScript');

            // 设置按钮为生成中状态
            convertToScript.disabled = true;
            convertToScript.innerHTML = '<span class="spinner" id="convertSpinner" style="display: inline-block;"></span><span>正在生成...</span>';

            const scriptSections = document.getElementById('scriptSections');
            scriptSections.innerHTML = ''; // 清空现有内容
            
            try {
                // 获取故事的语言信息
                const storyLanguage = document.getElementById('storyText').getAttribute('data-language') || 'english';

                const response = await fetch('/generate-script', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ story: storyText, language: storyLanguage })
                });

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                while (true) {
                    const { value, done } = await reader.read();
                    if (done) break;
                    
                    const lines = decoder.decode(value).split('\n');
                    for (const line of lines) {
                        if (!line) continue;
                        
                        const data = JSON.parse(line);
                        switch (data.type) {
                            case 'status':
                                convertToScript.textContent = data.message;
                                break;
                            case 'complete':
                                if (data.success && data.script && data.script.scenes) {
                                    // 为每个场景创建一个段落
                                    data.script.scenes.forEach((scene, index) => {
                                        const section = createScriptSection(scene, index);
                                        scriptSections.appendChild(section);
                                    });

                                    // 显示所有按钮
                                    showElement('generateStoryAudioBtn');
                                    showElement('downloadImages');
                                    
                                    // 添加脚本控制按钮
                                    const scriptControls = document.createElement('div');
                                    scriptControls.className = 'script-controls';
                                    scriptControls.style.marginTop = '20px';
                                    scriptControls.style.display = 'flex';
                                    scriptControls.style.gap = '10px';
                                    scriptControls.innerHTML = `
                                        <button onclick="addScriptSection('narration')" class="btn secondary">
                                            添加旁白
                                        </button>
                                        <button onclick="addScriptSection('dialogue')" class="btn secondary">
                                            添加对话
                                        </button>
                                        <button onclick="downloadScript('json')" class="btn secondary">
                                            下载 JSON
                                        </button>
                                        <button onclick="downloadScript('txt')" class="btn secondary">
                                            下载 TXT
                                        </button>
                                    `;
                                    scriptSections.appendChild(scriptControls);
                                    
                                    // 添加批量生成图片的按钮
                                    scriptSections.appendChild(createImageButtons());
                                }
                                break;
                            case 'error':
                                throw new Error(data.error);
                        }
                    }
                }
            } catch (error) {
                alert('转换脚本失败：' + error.message);
            } finally {
                // 恢复按钮状态
                convertToScript.disabled = false;
                convertToScript.style.backgroundColor = '';  // 清除内联样式，使用CSS类样式
                convertToScript.innerHTML = '<span class="spinner" id="convertSpinner"></span><span id="convertText">📝 转换为脚本</span>';
            }
        }

        // 修改 createImageButtons 函数
        function createImageButtons() {
            const buttonContainer = document.createElement('div');
            buttonContainer.className = 'button-container';
            buttonContainer.style.marginTop = '20px';
            buttonContainer.style.display = 'flex';
            buttonContainer.style.gap = '10px';
            buttonContainer.innerHTML = `
                <button onclick="generateStoryAudio()" id="generateStoryAudioBtn" class="primary-btn">
                    <span class="spinner" id="audioSpinner"></span>
                    <span id="storyBtnText">🎵 生成音频</span>
                </button>
                <div style="display: flex; align-items: center; gap: 10px;">
                    <label for="imageModelSelect" style="font-weight: bold; color: #333;">AI 模型：</label>
                    <select id="imageModelSelect" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; background: white;">
                        <option value="black-forest-labs/flux-schnell" selected>FLUX Schnell</option>
                        <option value="black-forest-labs/flux-dev">FLUX Dev</option>
                        <option value="black-forest-labs/flux-1.1-pro">FLUX 1.1 Pro</option>
                        <option value="black-forest-labs/flux-kontext-pro">FLUX Kontext Pro</option>
                        <option value="black-forest-labs/flux-kontext-max">FLUX Kontext Max</option>
                    </select>
                </div>
                <button onclick="generateAllImages()" id="generateAllImagesBtn" class="primary-btn">
                    生成所有图片
                </button>
                <button onclick="downloadAllImages()" class="primary-btn">
                    下载所有图片
                </button>
            `;
            return buttonContainer;
        }

        function createScriptSection(scene, index) {
            const section = document.createElement('div');
            section.className = 'script-section';
            section.draggable = true;
            section.id = `section-${Date.now()}-${index}`;
            
            // 添加拖动事件
            section.addEventListener('dragstart', handleDragStart);
            section.addEventListener('dragend', handleDragEnd);
            section.addEventListener('dragover', handleDragOver);
            section.addEventListener('drop', handleDrop);

            const header = document.createElement('div');
            header.className = 'section-header';

            // 添加类型标签
            const typeLabel = document.createElement('span');
            typeLabel.className = `section-type ${scene.type}`;
            typeLabel.textContent = scene.type === 'narration' ? '旁白' : '对话';
            header.appendChild(typeLabel);

            // 添加控制按钮
            const controls = document.createElement('div');
            controls.className = 'section-controls';

            // 添加语音选择
            const voiceSelect = document.createElement('select');
            voiceSelect.className = 'voice-select';
            const voiceOptionsHtml = voices.map(voice =>
                `<option value="${voice.id}">${voice.name} (${voice.language}, ${voice.gender})</option>`
            ).join('');
            voiceSelect.innerHTML = voiceOptionsHtml;

            // 根据故事语言和场景类型选择合适的默认语音
            const storyLanguage = document.getElementById('storyText').getAttribute('data-language') || 'english';
            const defaultGender = scene.type === 'dialogue' ? 'Male' : 'Female';
            voiceSelect.value = getDefaultVoiceForLanguage(storyLanguage, defaultGender);

            // 添加删除按钮
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'delete-btn';
            deleteBtn.textContent = 'Delete';
            deleteBtn.onclick = () => section.remove();

            controls.appendChild(voiceSelect);
            controls.appendChild(deleteBtn);
            header.appendChild(controls);

            const content = document.createElement('div');
            content.className = 'section-content';

            // 如果是对话，添加角色名显示
            if (scene.type === 'dialogue' && scene.character) {
                const characterLabel = document.createElement('div');
                characterLabel.className = 'character-label';
                characterLabel.textContent = scene.character;
                content.appendChild(characterLabel);
            }

            // 添加文本区域
            const textarea = document.createElement('textarea');
            textarea.className = 'text-input';
            textarea.value = scene.text;
            textarea.placeholder = scene.type === 'dialogue' ? '输入对话...' : '输入旁白...';
            content.appendChild(textarea);

            section.appendChild(header);
            section.appendChild(content);

            // 添加图片容器
            const imageContainer = document.createElement('div');
            imageContainer.className = 'section-image-container';
            const uniqueId = `section-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
            imageContainer.innerHTML = `
                <div class="image-controls">
                    <button class="generate-image-btn">生成图片</button>
                    <div class="model-select-container">
                        <label for="sectionModel-${uniqueId}" class="model-label">模型：</label>
                        <select id="sectionModel-${uniqueId}" class="section-model-select">
                            <option value="black-forest-labs/flux-schnell" selected>FLUX Schnell</option>
                            <option value="black-forest-labs/flux-dev">FLUX Dev</option>
                            <option value="black-forest-labs/flux-1.1-pro">FLUX 1.1 Pro</option>
                            <option value="black-forest-labs/flux-kontext-pro">FLUX Kontext Pro</option>
                            <option value="black-forest-labs/flux-kontext-max">FLUX Kontext Max</option>
                        </select>
                    </div>
                </div>
                <div class="image-loading" style="display: none;">
                    <span class="spinner"></span>
                    <span>正在生成图片...</span>
                </div>
                <div class="image-prompt" style="display: none;"></div>
                <div class="image-preview">
                    <img style="max-width: 100%; display: none;" />
                    <div class="regenerate-controls" style="display: none;">
                        <button class="regenerate-btn">重新生成</button>
                        <select class="regenerate-model-select">
                            <option value="black-forest-labs/flux-schnell">FLUX Schnell</option>
                            <option value="black-forest-labs/flux-dev">FLUX Dev</option>
                            <option value="black-forest-labs/flux-1.1-pro">FLUX 1.1 Pro</option>
                            <option value="black-forest-labs/flux-kontext-pro">FLUX Kontext Pro</option>
                            <option value="black-forest-labs/flux-kontext-max">FLUX Kontext Max</option>
                        </select>
                    </div>
                </div>
            `;
            
            // 绑定生成图片按钮事件
            const generateImageBtn = imageContainer.querySelector('.generate-image-btn');
            generateImageBtn.onclick = () => generateSectionImage(section, scene.text);
            
            section.appendChild(imageContainer);
            return section;
        }

        async function generateSectionImage(section, text, isRegenerate = false) {
            if (!text) {
                alert('没有可用于图像生成的文本');
                return;
            }

            const imageContainer = section.querySelector('.section-image-container');
            const generateBtn = imageContainer.querySelector('.generate-image-btn');
            const loading = imageContainer.querySelector('.image-loading');
            const preview = imageContainer.querySelector('.image-preview');
            const promptDiv = imageContainer.querySelector('.image-prompt');
            const img = preview.querySelector('img');
            const regenerateControls = preview.querySelector('.regenerate-controls');
            const regenerateBtn = preview.querySelector('.regenerate-btn');
            const regenerateModelSelect = preview.querySelector('.regenerate-model-select');

            generateBtn.disabled = true;
            loading.style.display = 'flex';
            img.style.display = 'none';
            promptDiv.style.display = 'none';
            regenerateControls.style.display = 'none';
            
            try {
                // 1. 生成提示词
                const promptResponse = await fetch('/generate-image-prompt', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        text: text,
                        context: document.getElementById('storyText').value 
                    })
                });
                
                if (!promptResponse.ok) {
                    throw new Error(`Failed to generate prompt: ${promptResponse.status}`);
                }
                
                const promptData = await promptResponse.json();
                if (!promptData.success || !promptData.prompt) {
                    throw new Error('Invalid prompt response');
                }

                // 2. 使用提示词生成图片
                let selectedModel;
                if (isRegenerate) {
                    // 重新生成时使用重新生成控件中的模型选择
                    selectedModel = regenerateModelSelect?.value || 'black-forest-labs/flux-schnell';
                } else {
                    // 首次生成时使用该段落的模型选择器
                    const sectionModelSelect = imageContainer.querySelector('.section-model-select');
                    selectedModel = sectionModelSelect?.value || 'black-forest-labs/flux-schnell';
                }

                const imageResponse = await fetch('/generate-image', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: promptData.prompt,
                        sectionId: section.id,
                        seed: isRegenerate ? Math.floor(Math.random() * 1000000) : 1234,
                        model: selectedModel
                    })
                });

                if (!imageResponse.ok) {
                    const errorText = await imageResponse.text();
                    console.error('Image generation HTTP error:', imageResponse.status, errorText);
                    throw new Error(`Image generation failed (${imageResponse.status}): ${errorText}`);
                }

                const imageData = await imageResponse.json();
                if (!imageData.success) {
                    console.error('Image generation API error:', imageData.error);
                    throw new Error(imageData.error || 'Image generation failed');
                }

                // 增强的 URL 处理逻辑
                let imageUrl;
                if (Array.isArray(imageData.imageUrl)) {
                    imageUrl = imageData.imageUrl[0];
                } else if (typeof imageData.imageUrl === 'string') {
                    imageUrl = imageData.imageUrl;
                } else if (typeof imageData.imageUrl === 'object' && imageData.imageUrl !== null) {
                    imageUrl = imageData.imageUrl.url || imageData.imageUrl.output || imageData.imageUrl.image;
                }

                // URL 验证
                if (!imageUrl || typeof imageUrl !== 'string') {
                    throw new Error('Invalid image URL format');
                }

                if (!imageUrl.startsWith('http')) {
                    throw new Error('Invalid image URL protocol');
                }

                // 3. 显示结果
                promptDiv.textContent = promptData.prompt;
                promptDiv.style.display = 'block';

                // 创建新图片元素
                const newImg = document.createElement('img');
                newImg.style.maxWidth = '100%';
                newImg.style.display = 'none';
                
                // 设置加载事件
                newImg.onload = () => {
                    loading.style.display = 'none';
                    newImg.style.display = 'block';
                    regenerateControls.style.display = 'flex';

                    // 设置重新生成模型选择器的默认值为当前使用的模型
                    if (regenerateModelSelect) {
                        regenerateModelSelect.value = selectedModel;
                    }
                };

                // 设置错误事件
                newImg.onerror = () => {
                    loading.style.display = 'none';
                    console.error('Failed to load image:', imageUrl);
                    throw new Error('Failed to load image from URL');
                };

                // 设置图片属性
                newImg.src = imageUrl;
                newImg.dataset.prompt = promptData.prompt;

                // 替换旧图片
                const oldImg = preview.querySelector('img');
                if (oldImg) {
                    preview.replaceChild(newImg, oldImg);
                } else {
                    preview.appendChild(newImg);
                }

                // 更新重新生成按钮的事件处理
                regenerateBtn.onclick = () => generateSectionImage(section, text, true);
                
            } catch (error) {
                console.error('Image generation error:', error);
                loading.style.display = 'none';
                alert('生成图像失败：' + error.message);
            } finally {
                generateBtn.disabled = false;
            }
        }

        function openImageModal(url, prompt) {
            const modal = document.createElement('div');
            modal.className = 'image-modal';
            modal.innerHTML = `
                <div class="modal-content">
                    <img src="${url}" alt="Generated image" />
                    <p class="prompt-text">${prompt}</p>
                    <button onclick="this.closest('.image-modal').remove()">Close</button>
                </div>
            `;
            document.body.appendChild(modal);
        }

        async function generateStoryAudio() {
            const sections = document.querySelectorAll('.script-section');
            const generateBtn = document.getElementById('generateStoryAudioBtn');
            const progressContainer = document.getElementById('storyProgressContainer');
            const progressBar = document.getElementById('storyProgress');
            const currentProgress = document.getElementById('storyCurrentProgress');
            const totalSections = document.getElementById('storyTotalSections');

            if (sections.length === 0) {
                showNotification('没有可生成音频的脚本段落', 'warning');
                return;
            }

            // 设置加载状态
            generateBtn.disabled = true;
            const spinner = generateBtn.querySelector('.spinner');
            const btnText = generateBtn.querySelector('#storyBtnText');
            spinner.style.display = 'inline-block';
            btnText.textContent = '🎵 正在生成故事音频...';
            progressContainer.style.display = 'block';
            progressBar.style.width = '0%';

            // 显示开始生成的通知
            showNotification('开始生成故事音频，请稍候...', 'info');

            try {
                const sectionsData = Array.from(sections).map(section => ({
                    text: section.querySelector('.text-input').value.trim(),
                    voice: section.querySelector('.voice-select').value
                })).filter(s => s.text && s.voice);

                if (sectionsData.length === 0) {
                    throw new Error('没有找到有效的文本和声音配置');
                }

                // 获取故事的语言信息
                const storyLanguage = document.getElementById('storyText')?.getAttribute('data-language') || 'english';

                const response = await fetch('/generate-and-merge', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ sections: sectionsData, language: storyLanguage })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let audioGenerated = false;

                while (true) {
                    const { value, done } = await reader.read();
                    if (done) break;

                    const lines = decoder.decode(value).split('\n');
                    for (const line of lines) {
                        if (!line.trim()) continue;

                        try {
                            const data = JSON.parse(line);
                            if (data.type === 'progress') {
                                currentProgress.textContent = data.current;
                                totalSections.textContent = data.total;
                                progressBar.style.width = `${(data.current / data.total) * 100}%`;
                            } else if (data.type === 'complete' && data.success) {
                                const audioPlayer = document.getElementById('storyAudioPlayer');
                                audioPlayer.src = data.audioUrl || data.filename;
                                audioPlayer.load();

                                audioGenerated = true;

                                // 显示成功通知
                                showNotification('🎉 故事音频生成成功！', 'success');

                                // 滚动到音频播放器
                                audioPlayer.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            } else if (data.type === 'error') {
                                throw new Error(data.message || '生成过程中发生错误');
                            }
                        } catch (e) {
                            console.warn('Failed to parse line:', line, e);
                        }
                    }
                }

                // 如果没有收到成功消息，显示警告
                if (!audioGenerated) {
                    showNotification('故事音频生成可能未完成，请检查结果', 'warning');
                }

            } catch (error) {
                console.error('Story audio generation error:', error);
                showNotification('生成故事音频失败：' + error.message, 'error');
            } finally {
                generateBtn.disabled = false;
                spinner.style.display = 'none';
                btnText.textContent = '🎵 生成音频';

                // 延迟隐藏进度条
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                    progressBar.style.width = '0%';
                }, 2000);
            }
        }

        let draggedElement = null;

        function handleDragStart(e) {
            draggedElement = this;
            this.classList.add('dragging');
            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text/html', this.innerHTML);
        }

        function handleDragEnd(e) {
            this.classList.remove('dragging');
            draggedElement = null;
            
            document.querySelectorAll('.drag-placeholder').forEach(p => p.remove());
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';

            const container = document.getElementById('scriptSections');
            const sections = [...container.children];
            
            if (this !== draggedElement) {
                const draggedRect = draggedElement.getBoundingClientRect();
                const targetRect = this.getBoundingClientRect();
                const draggedIndex = sections.indexOf(draggedElement);
                const targetIndex = sections.indexOf(this);
                
                if (draggedIndex < targetIndex) {
                    this.parentNode.insertBefore(draggedElement, this.nextSibling);
                } else {
                    this.parentNode.insertBefore(draggedElement, this);
                }
            }
        }

        function handleDrop(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        // Enhanced page switching function with accessibility support
        function switchPage(pageId) {
            // Update navigation button states
            document.querySelectorAll('.nav-button').forEach(btn => {
                btn.classList.remove('active');
                btn.setAttribute('aria-pressed', 'false');
            });

            const activeBtn = document.querySelector(`.nav-button[onclick*="${pageId}"]`);
            if (activeBtn) {
                activeBtn.classList.add('active');
                activeBtn.setAttribute('aria-pressed', 'true');
            }

            // Update page display with fade animation
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
                page.setAttribute('aria-hidden', 'true');
            });

            const activePage = document.getElementById(`${pageId}Page`);
            if (activePage) {
                activePage.classList.add('active');
                activePage.setAttribute('aria-hidden', 'false');

                // Focus management for accessibility
                const firstFocusable = activePage.querySelector('input, textarea, button, select');
                if (firstFocusable) {
                    setTimeout(() => firstFocusable.focus(), 100);
                }
            }

            // Update page title
            const pageTitles = {
                'story': '故事生成器',
                'simple': '简单语音合成',
                'multi': '多声音语音合成',
                'podcast': '播客生成器'
            };
            document.title = `${pageTitles[pageId]} - AI ContentCraft`;
        }

        // Enhanced Simple TTS generation function
        async function generateSimpleTTS() {
            const text = document.getElementById('simpleTtsText').value.trim();
            const voice = document.getElementById('simpleTtsVoice').value;

            if (!text) {
                showNotification('请输入要转换的文本', 'warning');
                document.getElementById('simpleTtsText').focus();
                return;
            }

            if (!voice) {
                showNotification('请选择一个声音', 'warning');
                document.getElementById('simpleTtsVoice').focus();
                return;
            }

            const button = document.getElementById('simpleTtsBtn');
            const spinner = document.getElementById('simpleTtsSpinner');
            const buttonText = document.getElementById('simpleTtsButtonText');

            // Update UI state
            setLoadingState(button, true, '🎵 正在生成音频...');

            try {
                announceToScreenReader('正在生成音频，请稍候');

                // 检测文本语言
                const detectedLanguage = detectLanguage(text);

                const response = await fetch('/generate-and-merge', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        sections: [{
                            text: text,
                            voice: voice
                        }],
                        language: detectedLanguage
                    })
                });

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                while (true) {
                    const { value, done } = await reader.read();
                    if (done) break;

                    const lines = decoder.decode(value).split('\n');
                    for (const line of lines) {
                        if (!line.trim()) continue;

                        try {
                            const data = JSON.parse(line);
                            if (data.type === 'complete' && data.success) {
                                const audioPlayer = document.getElementById('simpleTtsPlayer');
                                audioPlayer.src = data.audioUrl || data.filename;
                                audioPlayer.load();

                                showNotification('音频生成成功！', 'success');
                                announceToScreenReader('音频生成完成');

                                // Scroll to audio player
                                audioPlayer.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            }
                        } catch (e) {
                            console.warn('Failed to parse line:', line, e);
                        }
                    }
                }
            } catch (error) {
                console.error('TTS generation error:', error);
                showNotification('生成音频失败：' + error.message, 'error');
                announceToScreenReader('音频生成失败');
            } finally {
                setLoadingState(button, false, '', '🎵 生成音频');
            }
        }

        async function generateMultiAudio() {
            const sections = document.querySelectorAll('.script-section');
            if (sections.length === 0) {
                showNotification('没有可生成音频的部分', 'warning');
                return;
            }

            const generateBtn = document.getElementById('generateMultiBtn');
            const spinner = document.getElementById('multiSpinner');
            const buttonText = document.getElementById('multiButtonText');
            const progressContainer = document.getElementById('multiProgressContainer');
            const progressBar = document.getElementById('multiProgress');
            const currentProgress = document.getElementById('currentProgress');
            const totalSections = document.getElementById('totalSections');

            // 设置初始状态
            generateBtn.disabled = true;
            spinner.style.display = 'inline-block';
            buttonText.textContent = '🎵 正在生成音频...';
            progressContainer.style.display = 'block';
            totalSections.textContent = sections.length;
            currentProgress.textContent = '0';
            progressBar.style.width = '0%';

            // 显示开始生成的通知
            showNotification('开始生成多声音音频，请稍候...', 'info');

            try {
                // 收集所有段落数据
                const sectionsData = Array.from(sections).map(section => {
                    const textarea = section.querySelector('textarea');
                    const voiceSelect = section.querySelector('.voice-select');
                    return {
                        text: textarea.value.trim(),
                        voice: voiceSelect.value
                    };
                }).filter(s => s.text && s.voice);

                if (sectionsData.length === 0) {
                    throw new Error('没有找到有效的文本段落');
                }

                // 获取故事的语言信息
                const storyLanguage = document.getElementById('storyText')?.getAttribute('data-language') || 'english';

                // 一次性发送所有数据进行处理
                const response = await fetch('/generate-and-merge', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ sections: sectionsData, language: storyLanguage })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                if (data.success) {
                    // 更新音频播放器
                    const audioPlayer = document.getElementById('multiAudioPlayer');
                    audioPlayer.src = data.filename;
                    audioPlayer.load();

                    // 设置进度为 100%
                    currentProgress.textContent = sections.length;
                    progressBar.style.width = '100%';

                    // 显示成功通知
                    showNotification('🎉 多声音音频生成成功！', 'success');

                    // 滚动到音频播放器
                    audioPlayer.scrollIntoView({ behavior: 'smooth', block: 'center' });
                } else {
                    throw new Error(data.error || '音频生成失败');
                }
            } catch (error) {
                console.error('Multi-audio generation error:', error);
                showNotification('音频生成失败：' + error.message, 'error');
            } finally {
                // 恢复按钮状态
                generateBtn.disabled = false;
                spinner.style.display = 'none';
                buttonText.textContent = '🎵 生成音频';

                // 延迟隐藏进度条
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                    progressBar.style.width = '0%';
                }, 2000);
            }
        }

        // 添加播客相关的 JavaScript 函数
        async function generatePodcastContent() {
            const topic = document.getElementById('podcastTopic').value.trim();
            if (!topic) {
                alert('请输入一个主题');
                return;
            }

            // Get selected language
            const selectedLanguage = document.querySelector('input[name="podcastLanguage"]:checked').value;

            const button = document.getElementById('generatePodcastBtn');
            const spinner = document.getElementById('podcastSpinner');
            const btnText = document.getElementById('podcastBtnText');

            button.disabled = true;
            spinner.style.display = 'inline-block';
            btnText.textContent = '正在生成...';

            try {
                const response = await fetch('/generate-podcast', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ topic, language: selectedLanguage })
                });

                const data = await response.json();
                if (data.success) {
                    const podcastTopicElement = document.getElementById('podcastTopic');
                    podcastTopicElement.value = data.content;

                    // 保存播客语言信息到数据属性
                    podcastTopicElement.setAttribute('data-language', data.language || selectedLanguage);

                    showElement('podcastScriptEditor');
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                alert('生成播客内容失败：' + error.message);
            } finally {
                button.disabled = false;
                spinner.style.display = 'none';
                btnText.textContent = '生成播客内容';
            }
        }

        async function generatePodcastScript() {
            const content = document.getElementById('podcastTopic').value.trim();
            if (!content) {
                alert('请先生成或输入播客内容');
                return;
            }

            // 获取播客语言信息
            const podcastLanguage = document.getElementById('podcastTopic').getAttribute('data-language') ||
                                  document.querySelector('input[name="podcastLanguage"]:checked').value;

            const button = document.getElementById('generateScriptBtn');
            button.disabled = true;
            button.innerHTML = '<span>⏳</span> 正在转换...';

            try {
                const response = await fetch('/generate-podcast-script', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ content, language: podcastLanguage })
                });

                const data = await response.json();
                if (data.success) {
                    const container = document.getElementById('podcastSections');
                    container.innerHTML = '';

                    data.script.forEach((dialog, index) => {
                        const section = createPodcastDialog(dialog.host, dialog.text, index);
                        container.appendChild(section);
                    });

                    // 根据当前语言设置更新主持人语音
                    updatePodcastVoiceDefaults(podcastLanguage);
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                alert('转换为脚本失败：' + error.message);
            } finally {
                button.disabled = false;
                button.innerHTML = '<span>📝</span> 转换为脚本';
            }
        }

        function createPodcastDialog(host, text, index) {
            const section = document.createElement('div');
            section.className = 'podcast-dialog';
            section.draggable = true;
            section.dataset.index = index;

            // 添加拖拽事件
            section.addEventListener('dragstart', handleDragStart);
            section.addEventListener('dragend', handleDragEnd);
            section.addEventListener('dragover', handleDragOver);
            section.addEventListener('drop', handleDrop);

            const hostLabel = document.createElement('div');
            hostLabel.className = `host-label host-${host.toLowerCase()}`;
            hostLabel.textContent = `主持人 ${host}`;
            section.appendChild(hostLabel);

            const textarea = document.createElement('textarea');
            textarea.className = 'text-input';
            textarea.value = text;
            section.appendChild(textarea);

            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'delete-btn';
            deleteBtn.textContent = '删除';
            deleteBtn.onclick = () => section.remove();
            section.appendChild(deleteBtn);

            return section;
        }

        function addPodcastDialog(host) {
            const container = document.getElementById('podcastSections');
            const section = createPodcastDialog(host, '', container.children.length);
            container.appendChild(section);
        }

        async function generatePodcastAudio() {
            const sections = document.querySelectorAll('.podcast-dialog');
            const topic = document.getElementById('podcastTopic').value.trim();

            if (sections.length === 0) {
                showNotification('未找到对话部分', 'warning');
                return;
            }

            const hostAVoice = document.getElementById('hostAVoice').value;
            const hostBVoice = document.getElementById('hostBVoice').value;

            if (!hostAVoice || !hostBVoice) {
                showNotification('请为两位主持人选择声音', 'warning');
                return;
            }

            const button = document.getElementById('generatePodcastAudioBtn');
            const spinner = document.getElementById('podcastAudioSpinner');
            const btnText = document.getElementById('podcastAudioBtnText');
            const progressContainer = document.getElementById('podcastProgressContainer');
            const progress = document.getElementById('podcastProgress');
            const currentProgress = document.getElementById('podcastCurrentProgress');
            const totalSections = document.getElementById('podcastTotalSections');

            button.disabled = true;
            spinner.style.display = 'inline-block';
            btnText.textContent = '🎵 正在生成播客音频...';
            progressContainer.style.display = 'block';
            totalSections.textContent = sections.length;
            currentProgress.textContent = '0';
            progress.style.width = '0%';

            // 显示开始生成的通知
            showNotification('开始生成播客音频，请稍候...', 'info');

            try {
                const sectionsData = Array.from(sections).map(section => {
                    const host = section.querySelector('.host-label').textContent.includes('A') ? 'A' : 'B';
                    return {
                        text: section.querySelector('textarea').value.trim(),
                        voice: host === 'A' ? hostAVoice : hostBVoice
                    };
                }).filter(s => s.text);

                if (sectionsData.length === 0) {
                    throw new Error('没有找到有效的对话内容');
                }

                // 获取播客的语言信息
                const podcastLanguage = document.getElementById('podcastTopic').getAttribute('data-language') || 'english';

                const response = await fetch('/generate-and-merge', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        sections: sectionsData,
                        theme: topic.substring(0, 20),
                        language: podcastLanguage
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let audioGenerated = false;

                while (true) {
                    const { value, done } = await reader.read();
                    if (done) break;

                    const lines = decoder.decode(value).split('\n');
                    for (const line of lines) {
                        if (!line.trim()) continue;

                        try {
                            const data = JSON.parse(line);
                            switch (data.type) {
                                case 'progress':
                                    currentProgress.textContent = data.current;
                                    progress.style.width = `${(data.current / data.total) * 100}%`;
                                    break;
                                case 'status':
                                    btnText.textContent = data.message;
                                    break;
                                case 'complete':
                                    if (data.success) {
                                        const audioPlayer = document.getElementById('podcastAudioPlayer');
                                        audioPlayer.src = data.filename;
                                        audioPlayer.load();
                                        progress.style.width = '100%';
                                        audioGenerated = true;

                                        // 显示成功通知
                                        showNotification('🎉 播客音频生成成功！', 'success');

                                        // 滚动到音频播放器
                                        audioPlayer.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                    }
                                    break;
                                case 'error':
                                    throw new Error(data.error || '生成过程中发生错误');
                            }
                        } catch (e) {
                            console.warn('Failed to parse line:', line, e);
                        }
                    }
                }

                // 如果没有收到成功消息，显示警告
                if (!audioGenerated) {
                    showNotification('播客音频生成可能未完成，请检查结果', 'warning');
                }

            } catch (error) {
                console.error('Podcast audio generation error:', error);
                showNotification('生成播客音频失败：' + error.message, 'error');
            } finally {
                button.disabled = false;
                spinner.style.display = 'none';
                btnText.textContent = '🎵 生成播客音频';

                setTimeout(() => {
                    progressContainer.style.display = 'none';
                    progress.style.width = '0%';
                }, 2000);
            }
        }

        async function generateStoryImage() {
            const storyText = document.getElementById('storyText').value;
            if (!storyText) {
                alert('请先生成一个故事');
                return;
            }

            const btn = document.getElementById('generateImageBtn');
            const imgContainer = document.getElementById('storyImage');
            const imgDisplay = document.getElementById('storyImageDisplay');
            
            btn.disabled = true;
            btn.textContent = 'Generating...';
            imgDisplay.style.display = 'block';
            imgContainer.style.display = 'none';
            
            try {
                // 生成 prompt
                const promptResponse = await fetch('/generate-image-prompt', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ text: storyText })
                });
                
                if (!promptResponse.ok) {
                    throw new Error(`Prompt generation failed: ${promptResponse.status}`);
                }
                
                const promptData = await promptResponse.json();
                if (!promptData.success || !promptData.prompt) {
                    throw new Error('Invalid prompt response');
                }

                // 生成图片
                const selectedModel = document.getElementById('imageModelSelect')?.value || 'black-forest-labs/flux-schnell';
                const imageResponse = await fetch('/generate-image', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: promptData.prompt,
                        sectionId: 'story',
                        model: selectedModel
                    })
                });
                
                if (!imageResponse.ok) {
                    throw new Error(`Image generation failed: ${imageResponse.status}`);
                }
                
                const imageData = await imageResponse.json();
                console.log('Image generation response:', imageData);

                if (!imageData.success || !imageData.imageUrl) {
                    throw new Error('No image URL in response');
                }

                // 确保 imageUrl 是字符串
                if (typeof imageData.imageUrl !== 'string') {
                    throw new Error('Invalid image URL format');
                }

                // 设置图片
                imgContainer.onload = () => {
                    console.log('Image loaded successfully');
                    imgContainer.style.display = 'block';
                };
                
                imgContainer.onerror = (e) => {
                    console.error('Image load error:', e);
                    throw new Error('Failed to load image');
                };

                imgContainer.src = imageData.imageUrl;
                imgContainer.alt = 'Generated story image';
                
            } catch (error) {
                console.error('Image generation error:', error);
                alert('生成图像失败：' + error.message);
                imgContainer.style.display = 'none';
            } finally {
                btn.disabled = false;
                btn.textContent = '生成图片';
            }
        }

        // 添加批量下载功能
        async function downloadAllImages() {
            const sections = document.querySelectorAll('.script-section');
            const images = [];
            
            sections.forEach(section => {
                const img = section.querySelector('.image-preview img');
                const promptDiv = section.querySelector('.image-prompt');
                
                if (img && img.src && img.src.startsWith('http')) {
                    images.push({
                        url: img.src,
                        prompt: promptDiv.textContent || img.dataset.prompt || ''
                    });
                }
            });
            
            console.log('Images to download:', images); // 添加日志
            
            if (images.length === 0) {
                alert('没有可下载的图片');
                return;
            }
            
            const theme = document.getElementById('themeInput').value.trim() || 'story';
            
            try {
                const response = await fetch('/download-images', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ images, theme })
                });
                
                const data = await response.json();
                if (data.success) {
                    alert(`成功下载了 ${data.totalImages} 张图片到：\n${data.directory}\n\n已创建 gallery.html 文件用于预览。`);
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                console.error('Download error:', error);
                alert('下载图片失败：' + error.message);
            }
        }

        // 添加生成所有图片的函数
        async function generateAllImages() {
            const sections = Array.from(document.querySelectorAll('.script-section')).map(section => ({
                id: section.id,
                text: section.querySelector('.text-input').value
            }));

            if (sections.length === 0) {
                alert('未找到脚本部分');
                return;
            }

            const btn = document.getElementById('generateAllImagesBtn');
            btn.disabled = true;
            btn.textContent = 'Generating...';

            // 添加进度显示容器
            const progressContainer = document.createElement('div');
            progressContainer.className = 'generation-progress';
            progressContainer.innerHTML = `
                <div class="progress-text">Initializing...</div>
                <div class="progress-bar">
                    <div class="progress" style="width: 0%"></div>
                </div>
            `;
            btn.parentNode.insertBefore(progressContainer, btn.nextSibling);

            try {
                const selectedModel = document.getElementById('imageModelSelect')?.value || 'black-forest-labs/flux-schnell';
                const response = await fetch('/generate-all-images', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        sections: sections,
                        model: selectedModel
                    })
                });

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                while (true) {
                    const { value, done } = await reader.read();
                    if (done) break;

                    const lines = decoder.decode(value).split('\n');
                    for (const line of lines) {
                        if (!line) continue;

                        const data = JSON.parse(line);
                        const progressText = progressContainer.querySelector('.progress-text');
                        const progressBar = progressContainer.querySelector('.progress');

                        switch (data.type) {
                            case 'status':
                                progressText.textContent = data.message;
                                break;
                            case 'prompt_progress':
                                progressText.textContent = `${data.message} - 正在生成提示词...`;
                                progressBar.style.width = `${(data.current / data.total) * 50}%`; // 提示词生成占总进度的50%
                                break;
                            case 'image_progress':
                                progressText.textContent = `${data.message} - 正在生成图片...`;
                                progressBar.style.width = `${50 + (data.current / data.total) * 50}%`; // 图片生成占后50%
                                break;
                            case 'section_complete':
                                const section = document.getElementById(data.sectionId);
                                if (section) {
                                    const imageContainer = section.querySelector('.section-image-container');
                                    const preview = imageContainer.querySelector('.image-preview');
                                    const img = preview.querySelector('img');
                                    const promptDiv = imageContainer.querySelector('.image-prompt');
                                    const regenerateControls = preview.querySelector('.regenerate-controls');
                                    const regenerateBtn = preview.querySelector('.regenerate-btn');
                                    const regenerateModelSelect = preview.querySelector('.regenerate-model-select');
                                    const loading = imageContainer.querySelector('.image-loading');

                                    promptDiv.textContent = data.prompt;
                                    promptDiv.style.display = 'block';

                                    // 改进图片加载逻辑
                                    loading.style.display = 'flex';

                                    // 清除旧的事件监听器
                                    const newImg = img.cloneNode(false);

                                    newImg.onload = () => {
                                        console.log('Image loaded successfully:', data.imageUrl);
                                        loading.style.display = 'none';
                                        newImg.style.display = 'block';
                                        regenerateControls.style.display = 'flex';

                                        // 设置重新生成模型选择器的默认值
                                        if (regenerateModelSelect) {
                                            regenerateModelSelect.value = 'black-forest-labs/flux-schnell';
                                        }
                                    };

                                    newImg.onerror = () => {
                                        console.error('Failed to load image:', data.imageUrl);
                                        loading.style.display = 'none';
                                        alert(`加载第 ${data.sectionId} 部分的图片失败`);
                                    };

                                    // 设置新图片的属性
                                    newImg.src = data.imageUrl;
                                    newImg.dataset.prompt = data.prompt;
                                    newImg.style.maxWidth = '100%';
                                    newImg.style.display = 'none';

                                    // 替换旧图片
                                    img.parentNode.replaceChild(newImg, img);

                                    regenerateBtn.onclick = () => generateSectionImage(section, section.querySelector('.text-input').value, true);
                                }
                                break;
                            case 'complete':
                                progressText.textContent = '所有图片生成成功！';
                                progressBar.style.width = '100%';
                                setTimeout(() => {
                                    progressContainer.remove();
                                }, 3000);
                                break;
                            case 'error':
                                throw new Error(data.error);
                        }
                    }
                }
            } catch (error) {
                alert('生成图片失败：' + error.message);
                progressContainer.remove();
            } finally {
                btn.disabled = false;
                btn.textContent = '生成所有图片';
            }
        }

        // 添加新的函数来处理添加脚本段落
        function addScriptSection(type) {
            const scriptSections = document.getElementById('scriptSections');
            const scene = {
                type: type,
                text: '',
                character: type === 'dialogue' ? '角色名称' : undefined
            };
            
            // 在控制按钮之前插入新段落
            const controls = scriptSections.querySelector('.script-controls');
            const section = createScriptSection(scene, Date.now());
            scriptSections.insertBefore(section, controls);
        }

        // 添加下载脚本的函数
        async function downloadScript(format = 'json') {
            const scriptSections = document.getElementById('scriptSections');
            const sections = Array.from(scriptSections.querySelectorAll('.script-section'));
            
            if (format === 'json') {
                // JSON 格式下载 (保持不变)
                const scriptData = {
                    scenes: sections.map(section => {
                        const type = section.querySelector('.section-type').textContent.toLowerCase();
                        const text = section.querySelector('.text-input').value;
                        const characterLabel = section.querySelector('.character-label');
                        
                        return {
                            type: type,
                            text: text,
                            ...(characterLabel && { character: characterLabel.textContent })
                        };
                    })
                };

                const blob = new Blob([JSON.stringify(scriptData, null, 2)], { type: 'application/json' });
                downloadFile(blob, `script-${new Date().toISOString().replace(/[:.]/g, '-')}.json`);
            } else if (format === 'txt') {
                // TXT 格式下载 (添加翻译功能)
                try {
                    // 准备英文内容
                    const englishContent = sections.map(section => {
                        const type = section.querySelector('.section-type').textContent;
                        const text = section.querySelector('.text-input').value;
                        const characterLabel = section.querySelector('.character-label');
                        
                        if (characterLabel) {
                            return `[${type}]\n${characterLabel.textContent}:\n${text}`;
                        } else {
                            return `[${type}]\n${text}`;
                        }
                    }).join('\n\n');

                    // 发送翻译请求
                    const response = await fetch('/translate-story-script', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ script: englishContent })
                    });

                    const data = await response.json();
                    if (!data.success) {
                        throw new Error(data.error);
                    }

                    // 合并英文和中文内容
                    const downloadContent = englishContent + '\n\n=== 中文翻译 ===\n\n' + data.translation;

                    // 下载双语文件
                    const blob = new Blob([downloadContent], { type: 'text/plain' });
                    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                    downloadFile(blob, `story-script-bilingual-${timestamp}.txt`);

                } catch (error) {
                    alert('翻译失败：' + error.message);
                    
                    // 如果翻译失败，仍然下载英文版本
                    const blob = new Blob([englishContent], { type: 'text/plain' });
                    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                    downloadFile(blob, `story-script-${timestamp}.txt`);
                }
            }
        }

        // 辅助函数：处理文件下载
        function downloadFile(blob, filename) {
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }

        // 添加翻译和下载函数
        async function translateAndDownload() {
            const podcastSections = document.getElementById('podcastSections');
            if (!podcastSections || podcastSections.children.length === 0) {
                alert('请先生成播客脚本');
                return;
            }

            const translateBtn = document.getElementById('translateBtn');
            translateBtn.disabled = true;
            translateBtn.textContent = '正在翻译...';

            try {
                // 收集所有对话内容
                const dialogues = Array.from(podcastSections.querySelectorAll('.podcast-dialog')).map(dialog => {
                    const host = dialog.querySelector('.host-label').textContent;
                    const text = dialog.querySelector('textarea').value;
                    return `[${host}]\n${text}`;
                }).join('\n\n');

                // 发送翻译请求
                const response = await fetch('/translate-podcast', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ script: dialogues })
                });

                const data = await response.json();
                if (!data.success) {
                    throw new Error(data.error);
                }

                // 准备下载内容：原文和译文并排显示
                const downloadContent = dialogues + '\n\n=== 中文翻译 ===\n\n' + data.translation;

                // 下载文件
                const blob = new Blob([downloadContent], { type: 'text/plain' });
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                downloadFile(blob, `podcast-script-bilingual-${timestamp}.txt`);

            } catch (error) {
                alert('翻译失败：' + error.message);
            } finally {
                translateBtn.disabled = false;
                translateBtn.textContent = '翻译并下载';
            }
        }
    </script>
</body>
</html> 