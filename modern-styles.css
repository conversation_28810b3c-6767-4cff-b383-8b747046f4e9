/* ===== MODERN UI STYLES FOR AI CONTENTCRAFT ===== */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* ===== GLOBAL RESET AND BASE STYLES ===== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-accent) 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: var(--space-4);
  overflow-x: hidden;
}

/* ===== MAIN CONTAINER ===== */
.container {
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  padding: var(--space-6);
  width: 100%;
  max-width: 1600px;
  margin: var(--space-4) auto;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary), var(--primary-400), var(--color-primary));
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

/* ===== NAVIGATION BAR ===== */
.nav-bar {
  background: linear-gradient(135deg, var(--color-primary), var(--primary-700));
  padding: var(--space-4);
  margin-bottom: var(--space-6);
  border-radius: var(--radius-xl);
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
}

.nav-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.nav-button {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-inverse);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
  flex: 1;
  min-width: 140px;
  text-align: center;
}

.nav-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nav-button.active {
  background: var(--bg-primary);
  color: var(--color-primary);
  border-color: var(--bg-primary);
  box-shadow: var(--shadow-md);
  font-weight: var(--font-weight-semibold);
}

.nav-button.active:hover {
  transform: translateY(-1px);
}

/* ===== PAGE SYSTEM ===== */
.page {
  display: none;
  animation: fadeIn 0.5s ease-in-out;
}

.page.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* ===== TYPOGRAPHY ===== */
h1 {
  color: var(--color-primary);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-6);
  text-align: center;
  position: relative;
}

h1::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary), var(--primary-400));
  border-radius: var(--radius-full);
}

h2 {
  color: var(--color-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

h2::before {
  content: '';
  width: 4px;
  height: 24px;
  background: linear-gradient(180deg, var(--color-primary), var(--primary-400));
  border-radius: var(--radius-full);
}

h3 {
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-3);
}

/* ===== FORM ELEMENTS ===== */
.text-input,
.theme-input,
.topic-input,
.character-input {
  width: 100%;
  padding: var(--space-4);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-family: var(--font-family-sans);
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all var(--transition-normal);
  resize: vertical;
  min-height: 200px;
}

.text-input:focus,
.theme-input:focus,
.topic-input:focus,
.character-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: var(--bg-primary);
}

.text-input::placeholder,
.theme-input::placeholder,
.topic-input::placeholder,
.character-input::placeholder {
  color: var(--text-tertiary);
}

.theme-input {
  min-height: 48px;
  margin-bottom: var(--space-4);
}

.topic-input {
  min-height: 250px;
  margin-bottom: var(--space-4);
}

/* ===== STORY DISPLAY TEXT AREA ===== */
#storyText {
  min-height: 300px;
  font-family: var(--font-family-serif);
  line-height: var(--line-height-relaxed);
}

/* ===== PODCAST TOPIC TEXT AREA ===== */
#podcastTopic {
  min-height: 280px;
  line-height: var(--line-height-relaxed);
}

/* ===== SELECT ELEMENTS ===== */
.voice-select {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-family: var(--font-family-sans);
  background: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

.voice-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.voice-select-container {
  margin: var(--space-4) 0;
}

/* ===== BUTTON CONTAINERS ===== */
.button-container {
  display: flex;
  gap: var(--space-3);
  margin: var(--space-5) 0;
  flex-wrap: wrap;
  align-items: center;
}

/* ===== ENHANCED BUTTONS ===== */
button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  font-family: var(--font-family-sans);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  position: relative;
  overflow: hidden;
  min-height: 44px;
  background: linear-gradient(135deg, var(--color-primary), var(--primary-700));
  color: var(--text-inverse);
  box-shadow: var(--shadow-sm);
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

button:hover::before {
  left: 100%;
}

button:hover {
  background: linear-gradient(135deg, var(--color-primary-hover), var(--primary-800));
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: var(--shadow-sm) !important;
  background: var(--secondary-400) !important;
}

button:disabled::before {
  display: none;
}

/* ===== SPECIALIZED BUTTON VARIANTS ===== */
.primary-btn {
  background: linear-gradient(135deg, var(--color-primary), var(--primary-700));
  color: var(--text-inverse);
}

.action-btn {
  background: var(--bg-primary);
  color: var(--color-secondary);
  border: 1px solid var(--border-medium);
  box-shadow: var(--shadow-sm);
}

.action-btn:hover {
  background: var(--bg-secondary);
  border-color: var(--color-secondary);
  color: var(--color-secondary-hover);
}

.delete-btn {
  background: linear-gradient(135deg, var(--color-error), #dc2626);
  color: var(--text-inverse);
}

.delete-btn:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
}

/* ===== PROGRESS COMPONENTS ===== */
.progress-container {
  margin: var(--space-6) 0;
  display: none;
  background: var(--bg-secondary);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.progress-text {
  margin-bottom: var(--space-3);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  text-align: center;
}

.progress-bar {
  height: 8px;
  background: var(--border-light);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.progress {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--primary-400));
  width: 0%;
  transition: width var(--transition-normal);
  border-radius: var(--radius-full);
  position: relative;
  overflow: hidden;
}

.progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* ===== SPINNER COMPONENT ===== */
.spinner {
  display: none;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid currentColor;
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== AUDIO COMPONENTS ===== */
.audio-container {
  margin: var(--space-6) 0;
  padding: var(--space-4);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

audio {
  width: 100%;
  height: 54px;
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  outline: none;
}

audio:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ===== STORY PAGE LAYOUT ===== */
.story-page-layout {
  display: grid;
  grid-template-columns: 380px 1fr;
  gap: var(--space-6);
  min-height: 600px;
}

/* ===== PODCAST PAGE LAYOUT ===== */
.podcast-page-layout {
  display: grid;
  grid-template-columns: 380px 1fr;
  gap: var(--space-6);
  min-height: 600px;
}

.instructions-panel {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
  position: relative;
  overflow: hidden;
  height: fit-content;
  position: sticky;
  top: var(--space-4);
}

.instructions-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary), var(--primary-400), var(--color-primary));
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.story-generation-panel,
.podcast-generation-panel {
  min-width: 0; /* Prevents grid overflow */
}

.instructions-header {
  margin-bottom: var(--space-6);
  text-align: center;
}

.instructions-header h2 {
  color: var(--color-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-2);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

.instructions-subtitle {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.instruction-steps {
  margin-bottom: var(--space-6);
}

.instruction-step {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
  padding: var(--space-4);
  background: rgba(255, 255, 255, 0.5);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all var(--transition-normal);
  position: relative;
}

.instruction-step:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  background: rgba(255, 255, 255, 0.7);
}

.step-number {
  background: linear-gradient(135deg, var(--color-primary), var(--primary-700));
  color: var(--text-inverse);
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
  flex-shrink: 0;
  box-shadow: var(--shadow-sm);
}

.step-content {
  flex: 1;
}

.step-content h3 {
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-1);
}

.step-content p {
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
  line-height: var(--line-height-relaxed);
}

.step-arrow {
  color: var(--color-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  flex-shrink: 0;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; transform: translateX(0); }
  50% { opacity: 1; transform: translateX(3px); }
}

.tips-section {
  background: linear-gradient(135deg, var(--color-warning-light), var(--color-warning-lighter));
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  border: 1px solid var(--color-warning);
}

.tips-section h3 {
  color: var(--color-warning-dark);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-3);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.tips-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tips-list li {
  color: var(--color-warning-dark);
  font-size: var(--font-size-xs);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-2);
  padding-left: var(--space-4);
  position: relative;
}

.tips-list li::before {
  content: '💡';
  position: absolute;
  left: 0;
  top: 0;
}

.tips-list li:last-child {
  margin-bottom: 0;
}

/* ===== CONTAINER COMPONENTS ===== */
.story-container,
.script-container,
.simple-tts-container,
.multi-tts-container,
.podcast-container {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-5);
  margin-bottom: var(--space-5);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  position: relative;
  overflow: hidden;
}

.story-container::before,
.script-container::before,
.simple-tts-container::before,
.multi-tts-container::before,
.podcast-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--color-primary), var(--primary-400), var(--color-primary));
}

/* ===== TAB SYSTEM ===== */
.tabs-container {
  margin: var(--space-6) 0;
  width: 100%;
}

.tabs {
  display: flex;
  gap: var(--space-1);
  margin-bottom: var(--space-6);
  background: var(--bg-secondary);
  padding: var(--space-1);
  border-radius: var(--radius-lg);
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tabs::-webkit-scrollbar {
  display: none;
}

.tab-btn {
  padding: var(--space-3) var(--space-4);
  border: none;
  background: transparent;
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  white-space: nowrap;
  min-width: 100px;
  position: relative;
}

.tab-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  color: var(--color-primary);
}

.tab-btn.active {
  background: var(--bg-primary);
  color: var(--color-primary);
  box-shadow: var(--shadow-sm);
  font-weight: var(--font-weight-semibold);
}

.tab-btn.add-tab {
  background: linear-gradient(135deg, var(--color-warning), #d97706);
  color: var(--text-inverse);
  width: 40px;
  height: 40px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  border-radius: var(--radius-lg);
  min-width: 40px;
}

.tab-btn.add-tab:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
  transform: scale(1.05);
}

.tab-panels {
  position: relative;
}

.tab-panel {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.tab-panel.active {
  display: block;
}

/* 多声音语音合成页面中的文本框 - 较小高度 */
.tab-panel .text-input {
  min-height: 60px;
  max-height: 120px;
  height: auto;
  line-height: 1.5;
  overflow-y: auto;
}

/* ===== SCRIPT SECTIONS ===== */
.script-section {
  margin-bottom: var(--space-4);
  padding: var(--space-4);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  background: var(--bg-secondary);
  cursor: move;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

/* 脚本段落中的文本框 - 较小高度 */
.script-section .text-input {
  min-height: 60px;
  max-height: 120px;
  height: auto;
  line-height: 1.5;
  overflow-y: auto;
}

.script-section:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.script-section.dragging {
  opacity: 0.7;
  border: 2px dashed var(--color-primary);
  transform: rotate(2deg);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-3);
  flex-wrap: wrap;
  gap: var(--space-2);
}

.section-controls {
  display: flex;
  gap: var(--space-2);
  align-items: center;
  flex-wrap: wrap;
}

.section-type {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--text-inverse);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.section-type.narration {
  background: linear-gradient(135deg, var(--secondary-600), var(--secondary-700));
}

.section-type.dialogue {
  background: linear-gradient(135deg, var(--color-success), var(--success-600));
}

.section-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.character-label {
  font-weight: var(--font-weight-semibold);
  color: var(--color-primary);
  margin-bottom: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--bg-accent);
  border-radius: var(--radius-md);
  display: inline-block;
  font-size: var(--font-size-sm);
}

/* ===== PODCAST COMPONENTS ===== */
.topic-section {
  margin-bottom: var(--space-8);
}

.voice-selection {
  margin-bottom: var(--space-8);
  padding: var(--space-5);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.character-voices {
  display: flex;
  gap: var(--space-6);
  flex-wrap: wrap;
}

.voice-select-group {
  flex: 1;
  min-width: 200px;
}

.voice-select-group label {
  display: block;
  margin-bottom: var(--space-2);
  color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
}

.script-controls {
  display: flex;
  gap: var(--space-3);
  margin: var(--space-6) 0;
  flex-wrap: wrap;
}

.podcast-sections {
  margin: var(--space-6) 0;
}

.podcast-dialog {
  position: relative;
  padding: var(--space-4);
  margin-bottom: var(--space-4);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  background: var(--bg-secondary);
  transition: all var(--transition-normal);
}

.podcast-dialog:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-md);
}

/* 播客对话框中的文本框 - 较小高度 */
.podcast-dialog textarea {
  min-height: 60px;
  max-height: 120px;
  height: auto;
  line-height: 1.5;
  overflow-y: auto;
}

/* 保持主要文本框的原始大小 */
#storyText,
#podcastTopic,
#simpleTtsText {
  min-height: 200px !important;
  max-height: none !important;
  height: auto !important;
}

.host-label {
  position: absolute;
  top: var(--space-2);
  left: var(--space-2);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--text-inverse);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.host-a {
  background: linear-gradient(135deg, #007bff, #0056b3);
}

.host-b {
  background: linear-gradient(135deg, var(--color-success), var(--success-600));
}

/* ===== IMAGE COMPONENTS ===== */
.story-image-container,
.section-image-container {
  margin: var(--space-5) 0;
  text-align: center;
}

.section-image-container {
  padding: var(--space-4);
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.image-controls {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-3);
  flex-wrap: wrap;
}

.model-select-container {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.model-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  white-space: nowrap;
}

.section-model-select,
.regenerate-model-select {
  padding: var(--space-1) var(--space-2);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--surface-primary);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  min-width: 140px;
}

.section-model-select option:disabled,
.regenerate-model-select option:disabled,
#imageModelSelect option:disabled {
  color: var(--text-disabled);
  background-color: var(--surface-disabled);
  font-style: italic;
}

.regenerate-controls {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--space-2);
}

.generate-image-btn,
.regenerate-btn {
  padding: var(--space-2) var(--space-4);
  background: linear-gradient(135deg, var(--color-primary), var(--primary-700));
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
}

.generate-image-btn:hover,
.regenerate-btn:hover {
  background: linear-gradient(135deg, var(--color-primary-hover), var(--primary-800));
  transform: translateY(-1px);
}

.image-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-4);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.image-prompt {
  margin: var(--space-3) 0;
  padding: var(--space-3);
  background: var(--bg-accent);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-style: italic;
  border-left: 3px solid var(--color-primary);
}

.image-preview {
  margin-top: var(--space-3);
  position: relative;
}

.image-preview img {
  max-width: 100%;
  height: auto;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  transition: transform var(--transition-normal);
}

.image-preview img:hover {
  transform: scale(1.02);
}

/* ===== HISTORY COMPONENTS ===== */
.history-container {
  margin-top: var(--space-6);
  max-height: 300px;
  overflow-y: auto;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  padding: var(--space-4);
}

.history-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  border-bottom: 1px solid var(--border-light);
  transition: background var(--transition-fast);
}

.history-item:last-child {
  border-bottom: none;
}

.history-item:hover {
  background: var(--bg-accent);
  border-radius: var(--radius-md);
}

.history-text {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.voice-name {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin-top: var(--space-1);
}

/* ===== DRAG AND DROP ===== */
.drag-placeholder {
  border: 2px dashed var(--border-medium);
  margin-bottom: var(--space-4);
  height: 100px;
  border-radius: var(--radius-lg);
  background: var(--bg-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
  font-size: var(--font-size-sm);
  font-style: italic;
}

.drag-handle {
  cursor: move;
  padding: var(--space-1);
  margin-right: var(--space-2);
  color: var(--text-tertiary);
  font-size: var(--font-size-lg);
}

.drag-handle:hover {
  color: var(--color-primary);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1400px) {
  .story-page-layout,
  .podcast-page-layout {
    grid-template-columns: 360px 1fr;
    gap: var(--space-5);
  }
}

@media (max-width: 1024px) {
  .story-page-layout,
  .podcast-page-layout {
    grid-template-columns: 300px 1fr;
    gap: var(--space-4);
  }

  .instructions-panel {
    padding: var(--space-4);
  }

  .instruction-step {
    padding: var(--space-3);
  }

  .step-content h3 {
    font-size: var(--font-size-xs);
  }

  .step-content p {
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  body {
    padding: var(--space-2);
  }

  .container {
    padding: var(--space-4);
    margin: var(--space-2) auto;
    border-radius: var(--radius-xl);
  }

  .story-page-layout,
  .podcast-page-layout {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .instructions-panel {
    position: static;
    order: 2;
    margin-top: var(--space-4);
  }

  .story-generation-panel,
  .podcast-generation-panel {
    order: 1;
  }

  .nav-bar {
    flex-direction: column;
    gap: var(--space-2);
  }

  .nav-button {
    min-width: auto;
    flex: none;
  }

  h1 {
    font-size: var(--font-size-2xl);
  }

  h2 {
    font-size: var(--font-size-lg);
  }

  .button-container {
    flex-direction: column;
    gap: var(--space-3);
  }

  .button-row {
    flex-direction: column;
  }

  .character-voices {
    flex-direction: column;
    gap: var(--space-4);
  }

  .voice-select-group {
    min-width: auto;
  }

  .script-controls {
    flex-direction: column;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }

  .section-controls {
    width: 100%;
    justify-content: space-between;
  }

  .tabs {
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-bottom: var(--space-2);
  }

  .tab-btn {
    min-width: 120px;
  }

  .history-container {
    max-height: 200px;
  }

  .history-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
}

@media (max-width: 480px) {
  .container {
    padding: var(--space-3);
    border-radius: var(--radius-lg);
  }

  h1 {
    font-size: var(--font-size-xl);
  }

  .nav-bar {
    padding: var(--space-3);
  }

  .nav-button {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-xs);
  }

  .text-input,
  .theme-input,
  .topic-input {
    padding: var(--space-3);
    font-size: var(--font-size-sm);
    min-height: 150px;
  }

  #storyText {
    min-height: 200px;
  }

  #podcastTopic {
    min-height: 180px;
  }

  button {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-sm);
    min-height: 40px;
  }

  .story-container,
  .script-container,
  .simple-tts-container,
  .multi-tts-container,
  .podcast-container {
    padding: var(--space-4);
  }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1f2937;
    --bg-secondary: #111827;
    --bg-tertiary: #374151;
    --bg-accent: #1e3a8a;

    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-tertiary: #9ca3af;

    --border-light: #374151;
    --border-medium: #4b5563;
    --border-dark: #6b7280;
  }
}

/* ===== FOCUS STYLES FOR ACCESSIBILITY ===== */
*:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

button:focus,
.nav-button:focus,
.tab-btn:focus {
  outline: 2px solid var(--text-inverse);
  outline-offset: 2px;
}

/* ===== UTILITY CLASSES ===== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.hidden {
  display: none !important;
}

.visible {
  display: block !important;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.gap-2 {
  gap: var(--space-2);
}

.gap-4 {
  gap: var(--space-4);
}

.mt-4 {
  margin-top: var(--space-4);
}

.mb-4 {
  margin-bottom: var(--space-4);
}

.p-4 {
  padding: var(--space-4);
}

/* ===== LOADING STATES ===== */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* ===== SCROLLBAR STYLING ===== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--border-medium);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
}

/* ===== PRINT STYLES ===== */
@media print {
  body {
    background: white;
    color: black;
  }

  .nav-bar,
  .button-container,
  .script-controls,
  button {
    display: none !important;
  }

  .container {
    box-shadow: none;
    border: 1px solid #ccc;
  }

  .page {
    display: block !important;
  }
}

/* ===== ADDITIONAL FORM STYLES ===== */
.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  margin-bottom: var(--space-2);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-label::before {
  margin-right: var(--space-2);
}

/* ===== FOOTER STYLES ===== */
.footer {
  margin-top: var(--space-8);
  padding: var(--space-4);
  text-align: center;
  color: var(--text-tertiary);
  font-size: var(--font-size-sm);
  border-top: 1px solid var(--border-light);
}

/* ===== ENHANCED ANIMATIONS ===== */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.story-container,
.script-container,
.simple-tts-container,
.multi-tts-container,
.podcast-container {
  animation: slideInUp 0.6s ease-out;
}

.nav-bar {
  animation: slideInDown 0.6s ease-out;
}

.container {
  animation: scaleIn 0.8s ease-out;
}

/* ===== ENHANCED HOVER EFFECTS ===== */
.script-section:hover,
.podcast-dialog:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.nav-button:hover {
  transform: translateY(-3px);
}

.tab-btn:hover {
  transform: translateY(-1px);
}

/* ===== LOADING SKELETON ===== */
.skeleton {
  background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.skeleton-text {
  height: 1rem;
  border-radius: var(--radius-sm);
  margin-bottom: var(--space-2);
}

.skeleton-button {
  height: 44px;
  border-radius: var(--radius-lg);
  width: 120px;
}

/* ===== ENHANCED FOCUS INDICATORS ===== */
.nav-button:focus-visible,
.tab-btn:focus-visible,
button:focus-visible {
  outline: 3px solid var(--color-primary);
  outline-offset: 2px;
  box-shadow: 0 0 0 6px rgba(59, 130, 246, 0.2);
}

.text-input:focus-visible,
.theme-input:focus-visible,
.topic-input:focus-visible,
.voice-select:focus-visible {
  outline: 3px solid var(--color-primary);
  outline-offset: 2px;
  box-shadow: 0 0 0 6px rgba(59, 130, 246, 0.1);
}

/* ===== IMPROVED MOBILE INTERACTIONS ===== */
@media (hover: none) and (pointer: coarse) {
  button:hover,
  .nav-button:hover,
  .tab-btn:hover {
    transform: none;
  }

  .script-section:hover,
  .podcast-dialog:hover {
    transform: none;
  }
}

/* ===== HIGH CONTRAST MODE SUPPORT ===== */
@media (prefers-contrast: high) {
  :root {
    --border-light: #000000;
    --border-medium: #000000;
    --text-tertiary: #000000;
  }

  button {
    border: 2px solid currentColor;
  }

  .nav-button {
    border: 2px solid currentColor;
  }
}

/* ===== NOTIFICATION SYSTEM ===== */
.notification {
  position: fixed;
  top: var(--space-4);
  right: var(--space-4);
  z-index: var(--z-tooltip);
  max-width: 400px;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-light);
  opacity: 0;
  transform: translateY(-100%);
  animation: slideInNotification 0.3s ease-out forwards;
}

@keyframes slideInNotification {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.notification-content {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
}

.notification-icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.notification-message {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  line-height: var(--line-height-normal);
}

.notification-close {
  background: none;
  border: none;
  font-size: var(--font-size-lg);
  color: var(--text-tertiary);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  flex-shrink: 0;
  min-height: auto;
}

.notification-close:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
  transform: none;
}

.notification-success {
  border-left: 4px solid var(--color-success);
}

.notification-error {
  border-left: 4px solid var(--color-error);
}

.notification-warning {
  border-left: 4px solid var(--color-warning);
}

.notification-info {
  border-left: 4px solid var(--color-primary);
}

/* ===== FORM VALIDATION STYLES ===== */
.text-input.error,
.theme-input.error,
.topic-input.error,
.voice-select.error {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.text-input.error:focus,
.theme-input.error:focus,
.topic-input.error:focus,
.voice-select.error:focus {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

/* ===== ENHANCED LOADING STATES ===== */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: loading 1.5s infinite;
}

/* ===== MOBILE NOTIFICATION ADJUSTMENTS ===== */
@media (max-width: 768px) {
  .notification {
    top: var(--space-2);
    right: var(--space-2);
    left: var(--space-2);
    max-width: none;
  }

  .notification-content {
    padding: var(--space-3);
  }

  .notification-message {
    font-size: var(--font-size-xs);
  }
}
