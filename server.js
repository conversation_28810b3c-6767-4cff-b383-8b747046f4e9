import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs/promises';
import { exec } from 'child_process';
import { promisify } from 'util';
import OpenAI from 'openai';
import dotenv from 'dotenv';
import Replicate from "replicate";

dotenv.config();

// 定义 execAsync
const execAsync = promisify(exec);

// 调试模式控制
const DEBUG_MODE = process.env.NODE_ENV !== 'production';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const app = express();
const port = 3000;

// 先设置中间件
app.use(express.json());
app.use(express.static(__dirname));
app.use('/output', express.static(path.join(__dirname, 'output')));
app.use('/temp', express.static(path.join(__dirname, 'temp')));

// 创建 output 目录
await fs.mkdir(path.join(__dirname, 'output'), { recursive: true });

// 初始化 TTS (使用硅基流动 CosyVoice2-0.5B)
console.log('Initializing SiliconFlow CosyVoice2-0.5B TTS...');
console.log('SiliconFlow CosyVoice2-0.5B TTS ready to use');

// 在初始化 OpenAI 之前添加 Replicate 客户端
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

// MiniMax TTS 辅助函数
async function generateSpeechWithMiniMax(text, voiceId = "Wise_Woman", language = "english") {
    try {
        // 检查Replicate API密钥
        if (!process.env.REPLICATE_API_TOKEN || process.env.REPLICATE_API_TOKEN === 'your_replicate_token_here') {
            throw new Error('Replicate API token is not configured. Please set REPLICATE_API_TOKEN in your .env file.');
        }

        console.log(`Generating speech with MiniMax TTS: voice=${voiceId}, language=${language}, text length=${text.length}`);

        // 根据语言设置language_boost参数
        let languageBoost = "English";
        if (language === "chinese") {
            languageBoost = "Chinese";
        }

        const inputParams = {
            text: text,
            voice_id: voiceId,
            speed: 1,
            volume: 1,
            pitch: 0,
            sample_rate: 32000,
            bitrate: 128000,
            channel: "mono",
            language_boost: languageBoost,
            english_normalization: true
        };

        // console.log('MiniMax TTS input parameters:', JSON.stringify(inputParams, null, 2));

        // 使用 predictions API 来获得更好的控制
        const prediction = await replicate.predictions.create({
            version: "minimax/speech-02-turbo",
            input: inputParams
        });

        // console.log('Prediction created:', prediction.id);

        // 等待预测完成
        let finalPrediction = prediction;
        while (finalPrediction.status === 'starting' || finalPrediction.status === 'processing') {
            await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
            finalPrediction = await replicate.predictions.get(prediction.id);
            // console.log('Prediction status:', finalPrediction.status);
        }

        if (finalPrediction.status === 'succeeded') {
            console.log('MiniMax TTS completed successfully');
            return finalPrediction.output; // 返回音频文件URL
        } else {
            throw new Error(`Prediction failed with status: ${finalPrediction.status}, error: ${finalPrediction.error}`);
        }
    } catch (error) {
        console.error('MiniMax TTS error:', error);
        throw error;
    }
}

// 硅基流动 CosyVoice2-0.5B TTS 辅助函数
async function generateSpeechWithSiliconFlow(text, voiceId = "alex", language = "chinese") {
    try {
        // 检查硅基流动API密钥
        if (!process.env.SILICONFLOW_API_KEY || process.env.SILICONFLOW_API_KEY === 'your_siliconflow_api_key_here') {
            throw new Error('SiliconFlow API key is not configured. Please set SILICONFLOW_API_KEY in your .env file.');
        }

        console.log(`Generating speech with SiliconFlow CosyVoice2: voice=${voiceId}, language=${language}, text length=${text.length}`);

        // 映射语音ID到硅基流动格式
        const voiceMapping = {
            // 男声
            "alex": "FunAudioLLM/CosyVoice2-0.5B:alex",      // 沉稳男声
            "benjamin": "FunAudioLLM/CosyVoice2-0.5B:benjamin", // 低沉男声
            "charles": "FunAudioLLM/CosyVoice2-0.5B:charles",   // 磁性男声
            "david": "FunAudioLLM/CosyVoice2-0.5B:david",       // 欢快男声
            // 女声
            "anna": "FunAudioLLM/CosyVoice2-0.5B:anna",         // 沉稳女声
            "bella": "FunAudioLLM/CosyVoice2-0.5B:bella",       // 激情女声
            "claire": "FunAudioLLM/CosyVoice2-0.5B:claire",     // 温柔女声
            "diana": "FunAudioLLM/CosyVoice2-0.5B:diana",       // 欢快女声
            // 兼容原有的MiniMax音色名称
            "Wise_Woman": "FunAudioLLM/CosyVoice2-0.5B:claire",
            "Lively_Girl": "FunAudioLLM/CosyVoice2-0.5B:diana",
            "Gentle_Man": "FunAudioLLM/CosyVoice2-0.5B:alex",
            "Friendly_Person": "FunAudioLLM/CosyVoice2-0.5B:anna",
            "Deep_Voice_Man": "FunAudioLLM/CosyVoice2-0.5B:benjamin",
            "Calm_Woman": "FunAudioLLM/CosyVoice2-0.5B:claire",
            "Casual_Guy": "FunAudioLLM/CosyVoice2-0.5B:david"
        };

        const actualVoice = voiceMapping[voiceId] || "FunAudioLLM/CosyVoice2-0.5B:alex";

        // 根据语言添加情感控制
        let processedText = text;
        if (language === "chinese") {
            // 为中文添加自然的情感表达
            processedText = text;
        } else {
            // 英文文本
            processedText = text;
        }

        // 调用硅基流动语音合成API
        const response = await fetch('https://api.siliconflow.cn/v1/audio/speech', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${process.env.SILICONFLOW_API_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: "FunAudioLLM/CosyVoice2-0.5B",
                voice: actualVoice,
                input: processedText,
                response_format: "mp3",
                speed: 1.0,
                gain: 0.0
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`SiliconFlow TTS API error: ${response.status} ${response.statusText} - ${errorText}`);
        }

        // 返回音频数据的Buffer
        const audioBuffer = await response.arrayBuffer();
        console.log(`SiliconFlow TTS generated audio: ${audioBuffer.byteLength} bytes`);

        return Buffer.from(audioBuffer);
    } catch (error) {
        console.error('SiliconFlow TTS error:', error);
        throw error;
    }
}

// API 路由
app.get('/voices', async (req, res) => {
    console.log('GET /voices request received');
    // 硅基流动 CosyVoice2-0.5B 支持的语音列表 - 支持中英日韩+方言
    const voices = [
        // 男声
        { id: "alex", name: "Alex / 沉稳男声", language: "multilingual", gender: "Male" },
        { id: "benjamin", name: "Benjamin / 低沉男声", language: "multilingual", gender: "Male" },
        { id: "charles", name: "Charles / 磁性男声", language: "multilingual", gender: "Male" },
        { id: "david", name: "David / 欢快男声", language: "multilingual", gender: "Male" },
        // 女声
        { id: "anna", name: "Anna / 沉稳女声", language: "multilingual", gender: "Female" },
        { id: "bella", name: "Bella / 激情女声", language: "multilingual", gender: "Female" },
        { id: "claire", name: "Claire / 温柔女声", language: "multilingual", gender: "Female" },
        { id: "diana", name: "Diana / 欢快女声", language: "multilingual", gender: "Female" },
        // 兼容原有名称（映射到新音色）
        { id: "Wise_Woman", name: "Wise Woman / 智慧女声 (Claire)", language: "multilingual", gender: "Female" },
        { id: "Lively_Girl", name: "Lively Girl / 活泼女孩 (Diana)", language: "multilingual", gender: "Female" },
        { id: "Deep_Voice_Man", name: "Deep Voice Man / 深沉男声 (Benjamin)", language: "multilingual", gender: "Male" },
        { id: "Calm_Woman", name: "Calm Woman / 平静女声 (Anna)", language: "multilingual", gender: "Female" },
        { id: "Casual_Guy", name: "Casual Guy / 随性男声 (David)", language: "multilingual", gender: "Male" }
    ];
    res.json(voices);
});

app.post('/generate', async (req, res) => {
    const { text, voice = "alex", language = "chinese" } = req.body;

    try {
        // 使用硅基流动CosyVoice2生成音频
        const audioBuffer = await generateSpeechWithSiliconFlow(text, voice, language);

        // 创建临时文件保存音频
        const timestamp = Date.now();
        const tempDir = path.join(__dirname, 'temp');
        await fs.mkdir(tempDir, { recursive: true });

        const tempFile = path.join(tempDir, `audio-${timestamp}.mp3`);
        await fs.writeFile(tempFile, audioBuffer);

        // 返回音频文件路径（相对于服务器根目录）
        const audioUrl = `/temp/audio-${timestamp}.mp3`;

        res.json({
            success: true,
            audioUrl: audioUrl
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 修改生成并合并音频的路由
app.post('/generate-and-merge', async (req, res) => {
    const { sections, language = "english" } = req.body;
    console.log('=== /generate-and-merge request ===');
    console.log('Language received:', language);
    console.log('Sections count:', sections?.length);
    // console.log('Request body:', JSON.stringify(req.body, null, 2));

    if (!sections || sections.length === 0) {
        return res.status(400).json({
            success: false,
            error: '没有有效的文本段落'
        });
    }

    // 改进文件名生成逻辑
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const outputFile = path.join(__dirname, `output/${timestamp}/audio.mp3`);

    try {
        // 创建临时目录和输出目录
        const tempDir = path.join(__dirname, 'temp');
        const outputDir = path.join(__dirname, 'output', timestamp);
        await fs.mkdir(tempDir, { recursive: true });
        await fs.mkdir(outputDir, { recursive: true });

        // 生成所有音频文件
        const audioFiles = [];
        for (let i = 0; i < sections.length; i++) {
            const { text, voice } = sections[i];

            // 发送进度更新
            res.write(JSON.stringify({
                type: 'progress',
                current: i + 1,
                total: sections.length,
                message: `Generating audio for section ${i + 1}/${sections.length}`
            }) + '\n');

            try {
                // 使用硅基流动CosyVoice2生成音频
                const audioBuffer = await generateSpeechWithSiliconFlow(text, voice, language);

                const tempFile = path.join(tempDir, `temp-${i}.mp3`);
                await fs.writeFile(tempFile, audioBuffer);
                audioFiles.push(tempFile);
            } catch (error) {
                console.error(`Error generating audio for section ${i}:`, error);
                res.write(JSON.stringify({
                    type: 'error',
                    message: `Failed to generate audio for section ${i + 1}: ${error.message}`
                }) + '\n');
            }
        }

        if (audioFiles.length > 0) {
            // 发送合并开始通知
            res.write(JSON.stringify({
                type: 'status',
                message: 'Merging audio files...'
            }) + '\n');

            // 创建文件列表
            const listFile = path.join(tempDir, `list-${timestamp}.txt`);
            const fileList = audioFiles.map(f => `file '${f}'`).join('\n');
            await fs.writeFile(listFile, fileList);

            // 合并音频文件 (MP3格式)
            // 尝试多个可能的FFmpeg路径
            const possibleFFmpegPaths = [
                'ffmpeg', // 系统PATH中的ffmpeg
                '/usr/local/bin/ffmpeg',
                '/opt/homebrew/bin/ffmpeg',
                '/usr/bin/ffmpeg'
            ];

            let ffmpegPath = 'ffmpeg'; // 默认使用系统PATH

            // 检查FFmpeg是否可用
            try {
                await execAsync('ffmpeg -version');
                console.log('Using system ffmpeg from PATH');
            } catch (error) {
                console.log('System ffmpeg not found, trying alternative paths...');
                for (const path of possibleFFmpegPaths.slice(1)) {
                    try {
                        await execAsync(`"${path}" -version`);
                        ffmpegPath = path;
                        console.log(`Found ffmpeg at: ${path}`);
                        break;
                    } catch (e) {
                        continue;
                    }
                }
            }

            await execAsync(`"${ffmpegPath}" -f concat -safe 0 -i "${listFile}" -c:a libmp3lame -b:a 128k "${outputFile}"`);

            // 清理临时文件
            await Promise.all([
                ...audioFiles.map(f => fs.unlink(f)),
                fs.unlink(listFile)
            ]);
            
            await fs.rmdir(tempDir);

            // 发送完成消息
            res.write(JSON.stringify({
                type: 'complete',
                success: true,
                filename: path.relative(__dirname, outputFile)
            }));
            res.end();
        } else {
            throw new Error('No audio generated');
        }
    } catch (error) {
        console.error('Error generating and merging audio:', error);
        res.write(JSON.stringify({
            type: 'error',
            error: error.message
        }));
        res.end();
    }
});

// 修改生成故事的路由
app.post('/generate-story', async (req, res) => {
    const { theme, language = 'english' } = req.body;
    try {
        // 根据语言选择设置系统提示和用户提示
        let systemContent, userContent;

        if (language === 'chinese') {
            systemContent = '你是一位专业的故事作家。创作引人入胜且有趣的短篇故事，具有良好的情节发展。请用中文回复。';
            userContent = `请写一个关于"${theme}"的短篇故事，大约200字左右`;
        } else {
            systemContent = 'You are a professional story writer. Create engaging and interesting short stories with good plot development.';
            userContent = `Write a short story about "${theme}" in around 200 words`;
        }

        const messages = [
            {
                role: 'system',
                content: systemContent
            },
            {
                role: 'user',
                content: userContent
            }
        ];

        // 使用新的API适配器
        const response = await callTextGenerationAPI(messages, {
            model: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B', // 使用DeepSeek-R1-Distill-Qwen-7B模型
            temperature: 0.7,
            max_tokens: 1000
        });

        res.json({
            success: true,
            story: response.choices[0].message.content,
            language: language
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 修改生成脚本的路由
app.post('/generate-script', async (req, res) => {
    const { story, language = "english" } = req.body;

    // 发送开始消息
    res.write(JSON.stringify({
        type: 'status',
        message: '正在将故事转换为脚本...'
    }) + '\n');

    try {
        // 根据语言设置系统提示
        let systemContent;
        if (language === 'chinese') {
            systemContent = `你是一个专业的脚本转换助手。将故事转换为对话格式，严格按照以下JSON格式返回，不要添加任何其他文字：

{
  "scenes": [
    {
      "type": "narration",
      "text": "场景描述或旁白"
    },
    {
      "type": "dialogue",
      "character": "角色名称",
      "text": "对话内容"
    }
  ]
}

要求：
1. 保持中文内容，不要翻译成英文
2. 分离旁白和对话
3. 不要使用星号(*)或任何特殊格式字符
4. 只返回JSON，不要添加解释或其他文字
5. 确保JSON格式正确`;
        } else {
            systemContent = `You are a professional script converter. Convert stories into dialogue format and return ONLY the JSON format below, without any additional text:

{
  "scenes": [
    {
      "type": "narration",
      "text": "scene description or narration"
    },
    {
      "type": "dialogue",
      "character": "Character Name",
      "text": "dialogue content"
    }
  ]
}

Requirements:
1. Convert any non-English text to English first
2. Separate narration and dialogues
3. Do not use asterisks (*) or any special formatting characters
4. Return ONLY JSON, no explanations or other text
5. Ensure JSON format is correct`;
        }

        const messages = [
            {
                role: 'system',
                content: systemContent
            },
            {
                role: 'user',
                content: `Convert this story into script format:\n${story}`
            }
        ];

        // 使用新的API适配器
        const response = await callTextGenerationAPI(messages, {
            model: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
            temperature: 0.7,
            max_tokens: 2000
        });
        
        // 发送生成完成消息
        res.write(JSON.stringify({
            type: 'status',
            message: 'Processing script format...'
        }) + '\n');

        // 解析返回的 JSON 字符串
        const scriptContent = response.choices[0].message.content;
        console.log('Raw script content from AI:', scriptContent);

        let scriptData;
        try {
            // 多种方式尝试提取和修复JSON
            let jsonString = scriptContent.trim();

            // 预处理：清理可能导致JSON解析失败的字符
            jsonString = jsonString
                .replace(/[\u2018\u2019]/g, "'")  // 替换智能单引号
                .replace(/[\u201C\u201D]/g, '"')  // 替换智能双引号
                .replace(/\u2026/g, '...')        // 替换省略号
                .replace(/\r\n/g, '\n')          // 统一换行符
                .replace(/\r/g, '\n');           // 统一换行符

            // 方法1: 尝试直接解析
            try {
                scriptData = JSON.parse(jsonString);
            } catch (e) {
                console.log('Direct parse failed, trying extraction methods...');

                // 方法2: 提取代码块中的JSON
                const codeBlockMatch = jsonString.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
                if (codeBlockMatch) {
                    jsonString = codeBlockMatch[1];
                    scriptData = JSON.parse(jsonString);
                } else {
                    // 方法3: 提取第一个完整的JSON对象
                    const jsonMatch = jsonString.match(/\{[\s\S]*\}/);
                    if (jsonMatch) {
                        jsonString = jsonMatch[0];

                        // 简化的JSON修复方法
                        console.log('Attempting to fix JSON format...');

                        // 先尝试简单的修复
                        jsonString = jsonString
                            .replace(/,(\s*[}\]])/g, '$1')  // 移除多余的逗号
                            .replace(/([{,]\s*)(\w+):/g, '$1"$2":');  // 为属性名添加引号

                        // 如果还是失败，尝试手动构建JSON
                        try {
                            scriptData = JSON.parse(jsonString);
                        } catch (e3) {
                            console.log('JSON repair still failed, manually parsing content...');
                            // 手动解析内容，提取文本
                            const textMatches = scriptContent.match(/"text":\s*"([^"]+)"/g);
                            const typeMatches = scriptContent.match(/"type":\s*"([^"]+)"/g);
                            const characterMatches = scriptContent.match(/"character":\s*"([^"]+)"/g);

                            if (textMatches && typeMatches) {
                                const scenes = [];
                                for (let i = 0; i < textMatches.length; i++) {
                                    const text = textMatches[i].match(/"text":\s*"([^"]+)"/)[1];
                                    const type = typeMatches[i] ? typeMatches[i].match(/"type":\s*"([^"]+)"/)[1] : 'narration';
                                    const scene = { type, text };

                                    if (type === 'dialogue' && characterMatches[i]) {
                                        const character = characterMatches[i].match(/"character":\s*"([^"]+)"/)[1];
                                        scene.character = character;
                                    }
                                    scenes.push(scene);
                                }
                                scriptData = { scenes };
                            } else {
                                throw e3;
                            }
                        }

                        try {
                            scriptData = JSON.parse(jsonString);
                        } catch (e2) {
                            console.log('JSON repair failed, creating fallback structure');
                            throw e2;
                        }
                    } else {
                        // 方法4: 如果没有JSON格式，创建简单的脚本结构
                        console.log('No JSON found, creating simple script structure');
                        scriptData = {
                            scenes: [
                                {
                                    type: "narration",
                                    text: scriptContent.replace(/\*/g, '').trim()
                                }
                            ]
                        };
                    }
                }
            }

            // 验证和修复数据格式
            if (!scriptData.scenes) {
                scriptData = { scenes: [] };
            }
            if (!Array.isArray(scriptData.scenes)) {
                scriptData.scenes = [];
            }

            // 如果scenes为空，添加原始内容
            if (scriptData.scenes.length === 0) {
                scriptData.scenes.push({
                    type: "narration",
                    text: scriptContent.replace(/\*/g, '').trim()
                });
            }

            // 处理脚本内容，移除所有星号和清理文本
            scriptData.scenes = scriptData.scenes.map(scene => ({
                ...scene,
                text: (scene.text || '').replace(/\*/g, '').trim(),
                ...(scene.character && { character: scene.character.replace(/\*/g, '').trim() })
            }));

            console.log('Parsed script data:', JSON.stringify(scriptData, null, 2));

            // 发送完成消息
            res.write(JSON.stringify({
                type: 'complete',
                success: true,
                script: scriptData
            }) + '\n');
            res.end();
        } catch (error) {
            console.error('Script parsing error:', error);
            console.error('Raw script content:', scriptContent);

            // 作为最后的备用方案，创建一个简单的脚本结构
            const fallbackScript = {
                scenes: [
                    {
                        type: "narration",
                        text: scriptContent.replace(/\*/g, '').trim()
                    }
                ]
            };

            res.write(JSON.stringify({
                type: 'complete',
                success: true,
                script: fallbackScript
            }) + '\n');
            res.end();
        }
    } catch (error) {
        console.error('Script generation error:', error);
        res.write(JSON.stringify({
            type: 'error',
            error: error.message
        }) + '\n');
        res.end();
    }
});

// 添加播客生成路由
app.post('/generate-podcast', async (req, res) => {
    const { topic, language = 'english' } = req.body;

    if (DEBUG_MODE) {
        console.log('=== PODCAST GENERATION DEBUG ===');
        console.log('Topic:', topic);
        console.log('Language:', language);
        console.log('DeepSeek API Key:', process.env.DEEPSEEK_API_KEY ? 'Present' : 'Missing');
    }

    try {
        if (DEBUG_MODE) console.log('Making request to DeepSeek API...');

        // 根据语言选择设置系统提示和用户提示
        let systemContent, userContent;

        if (language === 'chinese') {
            systemContent = '你是一位专业的播客内容创作者。创作引人入胜且信息丰富的播客内容，适合两位主持人之间的对话。请用中文回复。';
            userContent = `请创建一个关于"${topic}"的播客讨论大纲。内容应该信息丰富且对话性强。`;
        } else {
            systemContent = 'You are a professional podcast content creator. Create engaging and informative podcast content that is suitable for a conversation between two hosts.';
            userContent = `Create a podcast discussion outline about "${topic}". The content should be informative and conversational.`;
        }

        const messages = [
            {
                role: 'system',
                content: systemContent
            },
            {
                role: 'user',
                content: userContent
            }
        ];

        // 使用新的API适配器
        const response = await callTextGenerationAPI(messages, {
            model: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
            temperature: 0.7,
            max_tokens: 1500
        });

        if (DEBUG_MODE) console.log('DeepSeek API response received successfully');
        res.json({
            success: true,
            content: response.choices[0].message.content,
            language: language
        });
    } catch (error) {
        console.error('=== PODCAST GENERATION ERROR ===');
        console.error('Error message:', error.message);
        console.error('Error status:', error.status);
        console.error('Error code:', error.code);
        console.error('Full error:', error);

        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

app.post('/generate-podcast-script', async (req, res) => {
    const { content, language = 'english' } = req.body;

    if (DEBUG_MODE) {
        console.log('=== PODCAST SCRIPT GENERATION DEBUG ===');
        console.log('Content length:', content ? content.length : 0);
        console.log('Language:', language);
        console.log('DeepSeek API Key:', process.env.DEEPSEEK_API_KEY ? 'Present' : 'Missing');
    }

    try {
        if (DEBUG_MODE) console.log('Making request to DeepSeek API for script generation...');

        // 根据语言设置系统提示
        let systemContent, userContent;

        if (language === 'chinese') {
            systemContent = `将内容转换为两位播客主持人（A和B）之间的自然中文对话。要求：
1. 将回复格式化为对话对象的JSON数组
2. 每个对象应该有'host'（'A'或'B'）和'text'字段
3. 保持对话自然且引人入胜
4. 保持中文内容，不要翻译成英文
格式示例：
[
    {"host": "A", "text": "欢迎收听我们的节目..."},
    {"host": "B", "text": "今天我们要讨论..."}
]`;
            userContent = `将以下内容转换为播客对话：\n${content}`;
        } else {
            systemContent = `Convert content into a natural English conversation between two podcast hosts (A and B). Requirements:
1. Format the response as JSON array of dialog objects
2. Each object should have 'host' (either 'A' or 'B') and 'text' fields
3. Keep the conversation natural and engaging
4. Convert any non-English content to English
Format example:
[
    {"host": "A", "text": "Welcome to our show..."},
    {"host": "B", "text": "Today we're discussing..."}
]`;
            userContent = `Convert this content into a podcast conversation:\n${content}`;
        }

        const messages = [
            {
                role: 'system',
                content: systemContent
            },
            {
                role: 'user',
                content: userContent
            }
        ];

        // 使用新的API适配器
        const response = await callTextGenerationAPI(messages, {
            model: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
            temperature: 0.7,
            max_tokens: 2000
        });

        if (DEBUG_MODE) console.log('DeepSeek API response received for script generation');
        const scriptContent = response.choices[0].message.content;
        if (DEBUG_MODE) {
            console.log('Generated script content:', scriptContent.substring(0, 500) + '...');
        }
        let scriptData;
        try {
            scriptData = JSON.parse(scriptContent);
        } catch (e) {
            const jsonMatch = scriptContent.match(/\[[\s\S]*\]/);
            if (jsonMatch) {
                scriptData = JSON.parse(jsonMatch[0]);
            } else {
                throw new Error('Invalid script format');
            }
        }

        res.json({
            success: true,
            script: scriptData
        });
    } catch (error) {
        console.error('=== PODCAST SCRIPT GENERATION ERROR ===');
        console.error('Error message:', error.message);
        console.error('Error status:', error.status);
        console.error('Error code:', error.code);
        console.error('Full error:', error);

        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 获取模型配置的辅助函数
function getModelConfig(modelName) {
    const configs = {
        'black-forest-labs/flux-schnell': {
            num_inference_steps: 4,
            guidance: 3.5,
            requiresInputImage: false
        },
        'black-forest-labs/flux-dev': {
            num_inference_steps: 50,
            guidance: 3.5,
            requiresInputImage: false
        },
        'black-forest-labs/flux-1.1-pro': {
            num_inference_steps: 25,
            guidance: 3.5,
            requiresInputImage: false
        },
        'black-forest-labs/flux-kontext-pro': {
            num_inference_steps: 30,
            guidance: 2.5,
            requiresInputImage: true
        },
        'black-forest-labs/flux-kontext-max': {
            num_inference_steps: 30,
            guidance: 2.5,
            requiresInputImage: true
        }
    };

    return configs[modelName] || configs['black-forest-labs/flux-schnell'];
}

// 修改 generate-image-prompt 路由
app.post('/generate-image-prompt', async (req, res) => {
    const { text, context } = req.body;
    
    if (!text) {
        return res.status(400).json({ 
            success: false, 
            error: 'Text is required' 
        });
    }

    console.log('Generating prompt for text:', text);
    
    try {
        const messages = [
            {
                role: 'system',
                content: `You are a professional image prompt engineer. Create concise but detailed image prompts that maintain consistency.

Requirements:
1. Keep prompts under 75 words
2. Focus on key visual elements and maintain character/setting consistency
3. Include artistic style and mood
4. Avoid NSFW content
5. Use natural, descriptive language
6. ALWAYS output in English only, regardless of input language
7. If input text is in Chinese or other languages, translate the key visual elements to English first

Story context:
${context || 'No context provided'}`
            },
            {
                role: 'user',
                content: `Create an English image generation prompt for this scene while maintaining consistency with any provided context. If the input text is not in English, translate the visual elements first: "${text}"`
            }
        ];

        // 使用新的API适配器
        const response = await callTextGenerationAPI(messages, {
            model: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
            temperature: 0.7,
            max_tokens: 500
        });

        const prompt = response.choices[0].message.content;
        console.log('Generated prompt:', prompt);
        
        res.json({ 
            success: true,
            prompt: prompt
        });
    } catch (error) {
        console.error('Prompt generation error:', error);
        res.status(500).json({ 
            success: false, 
            error: error.message || 'Failed to generate prompt'
        });
    }
});

// 修改 generate-image 路由
app.post('/generate-image', async (req, res) => {
    const {
        prompt,
        sectionId,
        seed = 1234,
        model = 'flux-schnell', // 默认使用硅基流动的FLUX-schnell
        width = 1024,
        height = 1024,
        num_inference_steps = 20,
        guidance_scale = 7.5
    } = req.body;

    if (!prompt) {
        return res.status(400).json({
            success: false,
            error: 'Prompt is required'
        });
    }

    console.log('Generating image for prompt:', prompt, 'using model:', model);

    try {
        // 使用新的图像生成API适配器
        const result = await callImageGenerationAPI(prompt, {
            model: model,
            image_size: `${width}x${height}`,
            batch_size: 1,
            num_inference_steps: num_inference_steps,
            guidance_scale: guidance_scale
        });

        console.log('Image generation result:', result);

        res.json({
            success: true,
            imageUrl: result.images[0].url,
            sectionId: sectionId,
            model: result.model,
            service: result.service,
            timings: result.timings,
            seed: result.seed
        });
    } catch (error) {
        console.error('=== IMAGE GENERATION ERROR ===');
        console.error('Model:', model);
        console.error('Prompt:', prompt);
        console.error('Error message:', error.message);
        console.error('Error details:', {
            message: error.message,
            stack: error.stack,
            name: error.name,
            status: error.status,
            code: error.code
        });

        // 提供更友好的错误信息
        let userFriendlyError = error.message;
        if (error.message.includes('400')) {
            userFriendlyError = 'Invalid request parameters. Please check your model selection and try again.';
        } else if (error.message.includes('401')) {
            userFriendlyError = 'Authentication failed. Please check your API credentials.';
        } else if (error.message.includes('429')) {
            userFriendlyError = 'Rate limit exceeded. Please wait a moment and try again.';
        } else if (error.message.includes('500')) {
            userFriendlyError = 'Server error occurred. Please try again later.';
        }

        res.status(500).json({
            success: false,
            error: userFriendlyError
        });
    }
});

// 修改 generate-all-images 路由
app.post('/generate-all-images', async (req, res) => {
    const { sections, model = 'flux-schnell' } = req.body; // 默认使用硅基流动的FLUX-schnell
    
    try {
        // 发送开始消息
        res.write(JSON.stringify({
            type: 'status',
            message: 'Analyzing story context...'
        }) + '\n');

        // 首先分析整个故事的上下文
        const contextMessages = [
            {
                role: 'system',
                content: `Extract key story elements (characters, settings, themes) from the story sections. Keep it concise.`
            },
            {
                role: 'user',
                content: `Analyze these story sections and extract key elements:\n${sections.map(s => s.text).join('\n\n')}`
            }
        ];

        const contextResponse = await callTextGenerationAPI(contextMessages, {
            model: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
            temperature: 0.7,
            max_tokens: 800
        });

        const storyContext = contextResponse.choices[0].message.content;
        console.log('Story context:', storyContext);

        // 发送提示词生成开始消息
        res.write(JSON.stringify({
            type: 'status',
            message: 'Generating prompts...'
        }) + '\n');

        // 生成所有提示词
        const promptResults = [];
        for (let i = 0; i < sections.length; i++) {
            const section = sections[i];
            
            res.write(JSON.stringify({
                type: 'prompt_progress',
                current: i + 1,
                total: sections.length,
                message: `Generating prompt ${i + 1}/${sections.length}`
            }) + '\n');

            const promptMessages = [
                {
                    role: 'system',
                    content: `You are a professional image prompt engineer. Create concise but detailed image prompts that maintain consistency across a story.

Requirements:
1. Keep prompts under 75 words
2. Focus on key visual elements and maintain character/setting consistency
3. Include artistic style and mood
4. Avoid NSFW content
5. Use natural, descriptive language
6. Output in English only

Story context:
${storyContext}`
                },
                {
                    role: 'user',
                    content: `Create an image generation prompt for this scene while maintaining consistency with the story context: "${section.text}"`
                }
            ];

            const promptResponse = await callTextGenerationAPI(promptMessages, {
                model: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
                temperature: 0.7,
                max_tokens: 500
            });

            promptResults.push({
                sectionId: section.id,
                prompt: promptResponse.choices[0].message.content
            });
        }

        // 发送开始生成图片的消息
        res.write(JSON.stringify({
            type: 'status',
            message: 'Generating images...'
        }) + '\n');

        // 生成所有图片
        for (let i = 0; i < promptResults.length; i++) {
            const { sectionId, prompt } = promptResults[i];
            
            res.write(JSON.stringify({
                type: 'image_progress',
                current: i + 1,
                total: promptResults.length,
                message: `Generating image ${i + 1}/${promptResults.length}`
            }) + '\n');

            try {
                // 使用新的图像生成API适配器
                const result = await callImageGenerationAPI(prompt, {
                    model: model,
                    image_size: '1024x1024',
                    batch_size: 1,
                    num_inference_steps: 20,
                    guidance_scale: 7.5
                });

                console.log('Image generation result:', result);

                res.write(JSON.stringify({
                    type: 'section_complete',
                    sectionId: sectionId,
                    prompt: prompt,
                    imageUrl: result.images[0].url,
                    model: result.model,
                    service: result.service,
                    current: i + 1,
                    total: promptResults.length
                }) + '\n');

            } catch (error) {
                console.error(`Error generating image for section ${sectionId}:`, error);
                console.error('Error details:', {
                    message: error.message,
                    stack: error.stack,
                    name: error.name
                });
                res.write(JSON.stringify({
                    type: 'section_error',
                    sectionId: sectionId,
                    error: error.message
                }) + '\n');
            }
        }

        // 发送完成消息
        res.write(JSON.stringify({
            type: 'complete',
            message: 'All images generated successfully'
        }));
        res.end();

    } catch (error) {
        res.write(JSON.stringify({
            type: 'error',
            error: error.message
        }));
        res.end();
    }
});

// 修改批量下载图片的路由
app.post('/download-images', async (req, res) => {
    const { images, theme } = req.body;
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    try {
        // 确保 output 目录存在
        const outputDir = path.join(__dirname, 'output');
        await fs.mkdir(outputDir, { recursive: true });
        
        // 直接使用时间戳创建目录
        const downloadDir = path.join(outputDir, timestamp);
        await fs.mkdir(downloadDir, { recursive: true });
        
        console.log('Downloading images to:', downloadDir);
        
        // 下载所有图片
        for (let i = 0; i < images.length; i++) {
            const { url, prompt } = images[i];
            console.log(`Downloading image ${i + 1}/${images.length} from:`, url);
            
            try {
                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
                }
                
                const arrayBuffer = await response.arrayBuffer();
                const buffer = Buffer.from(arrayBuffer);
                
                const filename = `image-${String(i + 1).padStart(3, '0')}.webp`;
                const filePath = path.join(downloadDir, filename);
                
                await fs.writeFile(filePath, buffer);
                console.log(`Saved image to:`, filePath);
                
                // 保存提示词到单独的文件
                await fs.appendFile(
                    path.join(downloadDir, 'prompts.txt'),
                    `Image ${i + 1}:\n${prompt}\nURL: ${url}\n\n`
                );
            } catch (error) {
                console.error(`Error downloading image ${i + 1}:`, error);
                await fs.appendFile(
                    path.join(downloadDir, 'errors.txt'),
                    `Failed to download image ${i + 1}:\nURL: ${url}\nError: ${error.message}\n\n`
                );
            }
        }
        
        // 创建一个简单的 HTML 预览文件，添加主题信息
        const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <title>${theme || 'Story'} - Image Gallery</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
        .theme { margin-bottom: 20px; color: #666; }
        .image-container { margin-bottom: 30px; }
        img { max-width: 100%; height: auto; border-radius: 8px; }
        .prompt { margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Generated Images</h1>
    <div class="theme">Theme: ${theme || 'Story'}</div>
    ${images.map((img, i) => `
        <div class="image-container">
            <img src="image-${String(i + 1).padStart(3, '0')}.webp" alt="Generated image ${i + 1}">
            <div class="prompt">
                <strong>Prompt ${i + 1}:</strong><br>
                ${img.prompt}
            </div>
        </div>
    `).join('')}
</body>
</html>`;
        
        await fs.writeFile(path.join(downloadDir, 'gallery.html'), htmlContent);
        
        res.json({
            success: true,
            directory: path.relative(__dirname, downloadDir),
            totalImages: images.length
        });
    } catch (error) {
        console.error('Error in download-images:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// AI 客户端配置
const openai = new OpenAI({
    baseURL: 'https://api.deepseek.com/v1',
    apiKey: process.env.DEEPSEEK_API_KEY || '', // 从环境变量读取
});

// 硅基流动客户端 (主要使用)
const siliconflowClient = new OpenAI({
    baseURL: 'https://api.siliconflow.cn/v1',
    apiKey: process.env.SILICONFLOW_API_KEY || '',
});

// 备用 OpenAI 客户端 (如果其他服务余额不足)
const openaiBackup = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY || '', // 需要添加 OpenAI API Key
});

// API 适配器函数 - 智能选择最佳的文本生成服务
async function callTextGenerationAPI(messages, options = {}) {
    const {
        model = 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B', // 使用DeepSeek-R1-Distill-Qwen-7B模型
        temperature = 0.7,
        max_tokens = 2000,
        useBackup = false
    } = options;

    try {
        // 优先使用硅基流动 (免费且性能好)
        if (!useBackup) {
            console.log('Using SiliconFlow API for text generation...');
            const response = await siliconflowClient.chat.completions.create({
                model: model,
                messages: messages,
                temperature: temperature,
                max_tokens: max_tokens
            });
            return response;
        }

        // 备用方案：使用DeepSeek
        console.log('Using DeepSeek API as backup...');
        const response = await openai.chat.completions.create({
            model: 'deepseek-chat',
            messages: messages,
            temperature: temperature,
            max_tokens: max_tokens
        });
        return response;

    } catch (error) {
        console.error('Primary API failed, trying backup...', error.message);

        // 如果主要API失败，尝试备用API
        if (!useBackup) {
            return await callTextGenerationAPI(messages, { ...options, useBackup: true });
        }

        // 如果备用也失败，抛出错误
        throw error;
    }
}

// 图像生成API适配器 - 智能选择最佳的图像生成服务
async function callImageGenerationAPI(prompt, options = {}) {
    const {
        model = 'flux-schnell', // 默认使用硅基流动的FLUX-schnell
        image_size = '1024x1024',
        batch_size = 1,
        num_inference_steps = 20,
        guidance_scale = 7.5,
        useReplicate = false
    } = options;

    try {
        // 根据模型选择API服务
        let shouldUseReplicate = useReplicate;
        let actualModel = model;

        // 智能选择：优先使用硅基流动的FLUX模型，Replicate作为备用
        if (model.includes('flux') || model.includes('FLUX') || model === 'auto') {
            shouldUseReplicate = false; // 优先使用硅基流动
            // 映射到硅基流动的FLUX模型名称
            if (model === 'flux-schnell' || model === 'auto') actualModel = 'black-forest-labs/FLUX.1-schnell';
            else if (model === 'flux-dev') actualModel = 'black-forest-labs/FLUX.1-dev';
            else if (model === 'flux-pro') actualModel = 'black-forest-labs/FLUX.1-pro';
            else actualModel = 'black-forest-labs/FLUX.1-schnell'; // 默认
        } else if (model === 'kolors') {
            shouldUseReplicate = false;
            actualModel = 'Kwai-Kolors/Kolors';
        }

        if (!shouldUseReplicate) {
            // 使用硅基流动 Kolors 模型
            console.log('Using SiliconFlow API for image generation with Kolors...');

            const response = await fetch('https://api.siliconflow.cn/v1/images/generations', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${process.env.SILICONFLOW_API_KEY}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: actualModel,
                    prompt: prompt,
                    image_size: image_size,
                    batch_size: batch_size,
                    num_inference_steps: num_inference_steps,
                    guidance_scale: guidance_scale
                })
            });

            if (!response.ok) {
                throw new Error(`SiliconFlow API error: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();

            // 转换为统一格式
            return {
                images: data.images,
                timings: data.timings,
                seed: data.seed,
                service: 'siliconflow',
                model: actualModel
            };
        } else {
            // 使用Replicate FLUX模型
            console.log(`Using Replicate API for image generation with ${actualModel}...`);

            const input = {
                prompt: prompt,
                width: parseInt(image_size.split('x')[0]),
                height: parseInt(image_size.split('x')[1]),
                num_inference_steps: num_inference_steps,
                guidance_scale: guidance_scale,
                num_outputs: batch_size
            };

            const output = await replicate.run(actualModel, { input });

            // 转换为统一格式
            const images = Array.isArray(output) ? output.map(url => ({ url })) : [{ url: output }];

            return {
                images: images,
                timings: { inference: 0 }, // Replicate不提供时间信息
                seed: Math.floor(Math.random() * 1000000),
                service: 'replicate',
                model: actualModel
            };
        }

    } catch (error) {
        console.error('Image generation failed:', error.message);

        // 如果硅基流动失败，尝试Replicate作为备用
        if (!shouldUseReplicate && !useReplicate) {
            console.log('Trying Replicate as backup...');
            return await callImageGenerationAPI(prompt, {
                ...options,
                useReplicate: true,
                model: 'flux-schnell' // 使用快速FLUX模型作为备用
            });
        }

        throw error;
    }
}

// 在 app.use 中间件部分添加
app.use((req, res, next) => {
    // 只允许本地域名访问
    const allowedOrigins = ['http://localhost:3000', 'http://127.0.0.1:3000'];
    const origin = req.headers.origin;
    
    if (allowedOrigins.includes(origin)) {
        res.header('Access-Control-Allow-Origin', origin);
        res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
    }
    next();
});

// 添加 API 测试端点
app.get('/test-apis', async (req, res) => {
    const results = {
        siliconflow: { status: 'unknown', error: null },
        deepseek: { status: 'unknown', error: null },
        replicate: { status: 'unknown', error: null }
    };

    // 测试硅基流动 API
    try {
        if (DEBUG_MODE) {
            console.log('Testing SiliconFlow API...');
            console.log('API Key:', process.env.SILICONFLOW_API_KEY ? 'Present' : 'Missing');
        }

        const response = await callTextGenerationAPI([
            { role: 'user', content: 'Hello, this is a test.' }
        ], {
            model: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
            max_tokens: 10
        });

        results.siliconflow.status = 'success';
        if (DEBUG_MODE) console.log('SiliconFlow API test successful');
    } catch (error) {
        results.siliconflow.status = 'error';
        results.siliconflow.error = {
            message: error.message,
            status: error.status,
            code: error.code
        };
        console.error('SiliconFlow API test failed:', error.message);
    }

    // 测试 DeepSeek API (备用)
    try {
        if (DEBUG_MODE) {
            console.log('Testing DeepSeek API...');
            console.log('API Key:', process.env.DEEPSEEK_API_KEY ? 'Present' : 'Missing');
        }

        const response = await openai.chat.completions.create({
            model: 'deepseek-chat',
            messages: [{ role: 'user', content: 'Hello, this is a test.' }],
            max_tokens: 10
        });

        results.deepseek.status = 'success';
        if (DEBUG_MODE) console.log('DeepSeek API test successful');
    } catch (error) {
        results.deepseek.status = 'error';
        results.deepseek.error = {
            message: error.message,
            status: error.status,
            code: error.code
        };
        console.error('DeepSeek API test failed:', error.message);
    }

    // 测试 Replicate API
    try {
        if (DEBUG_MODE) {
            console.log('Testing Replicate API...');
            console.log('API Token:', process.env.REPLICATE_API_TOKEN ? 'Present' : 'Missing');
        }

        // 简单的模型列表请求来测试连接
        const response = await fetch('https://api.replicate.com/v1/models', {
            headers: {
                'Authorization': `Token ${process.env.REPLICATE_API_TOKEN}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            results.replicate.status = 'success';
            if (DEBUG_MODE) console.log('Replicate API test successful');
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
    } catch (error) {
        results.replicate.status = 'error';
        results.replicate.error = {
            message: error.message
        };
        console.error('Replicate API test failed:', error.message);
    }

    res.json(results);
});

// 添加翻译路由
app.post('/translate-podcast', async (req, res) => {
    const { script } = req.body;
    
    try {
        const messages = [
            {
                role: 'system',
                content: `Translate the podcast script to Chinese. Keep the format:
1. Keep the Host A/B labels
2. Translate naturally and maintain the conversation style
3. Return in this format:
[Host A]
Chinese translation

[Host B]
Chinese translation
`
            },
            {
                role: 'user',
                content: `Translate this podcast script to Chinese:\n${script}`
            }
        ];

        const response = await callTextGenerationAPI(messages, {
            model: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
            temperature: 0.7,
            max_tokens: 2000
        });
        
        res.json({ 
            success: true,
            translation: response.choices[0].message.content 
        });
    } catch (error) {
        res.status(500).json({ 
            success: false, 
            error: error.message 
        });
    }
});

// 添加故事脚本翻译路由
app.post('/translate-story-script', async (req, res) => {
    const { script } = req.body;
    
    try {
        const messages = [
            {
                role: 'system',
                content: `Translate the story script to Chinese. Keep the format:
1. Keep the [Narration] and [Dialogue] labels
2. Translate naturally and maintain the story flow
3. Return in this format:
[Narration]
Chinese translation

[Dialogue]
Character Name:
Chinese translation
`
            },
            {
                role: 'user',
                content: `Translate this story script to Chinese:\n${script}`
            }
        ];

        const response = await callTextGenerationAPI(messages, {
            model: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
            temperature: 0.7,
            max_tokens: 2000
        });
        
        res.json({ 
            success: true,
            translation: response.choices[0].message.content 
        });
    } catch (error) {
        res.status(500).json({ 
            success: false, 
            error: error.message 
        });
    }
});

// 启动服务器
app.listen(port, () => {
    console.log(`Server running at http://localhost:${port}`);
});
