/* ===== CSS VARIABLES (DESIGN SYSTEM) ===== */
:root {
  /* Color Palette */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  --secondary-50: #f8fafc;
  --secondary-100: #f1f5f9;
  --secondary-200: #e2e8f0;
  --secondary-300: #cbd5e1;
  --secondary-400: #94a3b8;
  --secondary-500: #64748b;
  --secondary-600: #475569;
  --secondary-700: #334155;
  --secondary-800: #1e293b;
  --secondary-900: #0f172a;

  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;

  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;

  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;

  /* Semantic Colors */
  --color-primary: var(--primary-600);
  --color-primary-hover: var(--primary-700);
  --color-primary-light: var(--primary-50);
  --color-secondary: var(--secondary-600);
  --color-secondary-hover: var(--secondary-700);
  --color-success: var(--success-500);
  --color-warning: var(--warning-500);
  --color-warning-light: var(--warning-100);
  --color-warning-lighter: var(--warning-50);
  --color-warning-dark: var(--warning-700);
  --color-error: var(--error-500);

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: var(--secondary-50);
  --bg-tertiary: var(--secondary-100);
  --bg-accent: var(--primary-50);

  /* Text Colors */
  --text-primary: var(--secondary-900);
  --text-secondary: var(--secondary-600);
  --text-tertiary: var(--secondary-400);
  --text-inverse: #ffffff;

  /* Border Colors */
  --border-light: var(--secondary-200);
  --border-medium: var(--secondary-300);
  --border-dark: var(--secondary-400);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Typography */
  --font-family-sans: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;

  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
}

/* ===== ENHANCED BUTTON COMPONENTS ===== */
.script-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  padding: var(--space-4);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  background: var(--bg-secondary);
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
}

.script-buttons:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-md);
}

.button-row {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-5);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  font-family: var(--font-family-sans);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  position: relative;
  overflow: hidden;
  min-height: 44px; /* Accessibility: minimum touch target */
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.btn:hover::before {
  left: 100%;
}

.btn.primary {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  border: 2px solid #2563eb;
  font-weight: 600;
}

.btn.primary:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
  transform: translateY(-2px);
  border-color: #1d4ed8;
}

.btn.primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn.secondary {
  background: var(--bg-primary);
  color: var(--color-secondary);
  border: 1px solid var(--border-medium);
  box-shadow: var(--shadow-sm);
}

.btn.secondary:hover {
  background: var(--bg-secondary);
  border-color: var(--color-secondary);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn.secondary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.btn:disabled::before {
  display: none;
}

/* 特殊突出的转换为脚本按钮样式 */
.btn.convert-script {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  color: white;
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  border: 3px solid #2563eb;
  font-weight: 700;
  font-size: 1.1em;
  padding: var(--space-4) var(--space-6);
  position: relative;
  overflow: hidden;
}

.btn.convert-script::before {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.btn.convert-script:hover {
  background: linear-gradient(135deg, #2563eb, #1e3a8a);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.5);
  transform: translateY(-3px);
  border-color: #1e3a8a;
}

.btn.convert-script:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.bottom-buttons {
  display: flex;
  gap: var(--space-3);
  margin-top: var(--space-5);
  flex-wrap: wrap;
}

.bottom-buttons .btn {
  flex: 1;
  min-width: 120px;
}

/* ===== LANGUAGE SELECTION STYLES ===== */
.language-label {
  display: block;
  font-weight: 600;
  color: var(--color-secondary);
  margin-bottom: var(--space-2);
  font-size: 0.9rem;
}

.language-options {
  display: flex;
  gap: var(--space-4);
  align-items: center;
  flex-wrap: wrap;
}

.radio-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: var(--space-2) var(--space-3);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  transition: all 0.2s ease;
  position: relative;
  min-width: 120px;
  justify-content: center;
}

.radio-option:hover {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
  transform: translateY(-1px);
}

.radio-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.radio-option input[type="radio"]:checked + .radio-text {
  color: var(--color-primary);
  font-weight: 600;
}

.radio-option input[type="radio"]:checked {
  + .radio-text::before {
    content: "✓ ";
    color: var(--color-success);
    font-weight: bold;
  }
}

.radio-option:has(input[type="radio"]:checked) {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.radio-text {
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  user-select: none;
}